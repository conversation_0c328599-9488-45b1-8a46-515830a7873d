# SVG Viewer App Technical Context

## Technology Stack

### Core Framework
- **React Native CLI** (non-Expo): For direct native module access
- **TypeScript**: For type safety and better development experience
- **React**: UI component library

### Navigation
- **React Navigation**: Navigation library with native stack and potentially drawer/tabs

### SVG Rendering
- **react-native-svg**: Native SVG rendering capabilities
- **react-native-webview**: Alternative rendering option

### File System & Sharing
- **@react-native-documents/picker**: For selecting files from device storage
- **react-native-fs**: File system operations
- **react-native-receive-sharing-intent**: Handling files from other apps
- **pako**: For decompressing SVGZ files

### Cloud Storage
- **axios**: HTTP client for API calls
- **@react-native-google-signin/google-signin**: Google authentication
- **react-native-msal**: Microsoft authentication (OneDrive)

### Image Processing
- **react-native-view-shot**: For capturing rendered SVGs as images

### State Management
- **Redux Toolkit**: For global state management
- **Redux Persist**: For persisting state to storage
- **@react-native-async-storage/async-storage**: Storage engine

### Gesture Handling
- **react-native-gesture-handler**: Advanced gesture recognition
- **react-native-reanimated**: For performant animations

### In-App Purchases
- **react-native-iap**: For handling in-app purchases

### UI & Styling
- **styled-components**: For component styling
- **react-native-vector-icons**: For icons

## Development Setup
1. React Native CLI project initialized with TypeScript template
2. ESLint and Prettier for code quality
3. Directory structure following feature-based organization

## Technical Constraints
1. **Native Module Access**: Need direct access to native modules for file handling and rendering
2. **Performance**: Complex SVGs require optimized rendering approaches
3. **Android Focus**: Initially targeting Android platform
4. **File Access**: Working within mobile OS restrictions for file access

## Dependencies
Core dependencies as listed in package.json, including:
- react: 19.0.0
- react-native: 0.79.2

Additional dependencies to be added:
- Navigation, SVG rendering, file handling, cloud integration, and other libraries as outlined above

## Build & Deployment
- Standard React Native build process using Gradle for Android
- Google Play Store deployment with in-app purchases configured
- Proper version management for updates

## Testing Strategy
1. **Unit Tests**: For utility functions and services
2. **Component Tests**: For UI components
3. **Integration Tests**: For core user flows
4. **Manual Testing**: For gesture interactions and rendering accuracy

## Performance Considerations
1. Optimized SVG rendering for complex files
2. Efficient memory management for large files
3. Smooth animations for zoom/pan gestures
4. Background processing for file conversion 