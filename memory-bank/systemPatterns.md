# SVG Viewer App System Patterns

## Architecture Overview
The application follows a component-based architecture using React Native. The system is designed with modularity in mind, separating concerns into distinct layers:

1. **UI Layer**: React components for user interface
2. **Business Logic**: Services for handling file operations, cloud integration, etc.
3. **State Management**: Redux for global state, local state for component-specific data
4. **Storage**: File system operations and AsyncStorage for persisting user data

## Design Patterns

### Component Structure
- **Screens**: High-level components representing full app screens (HomeScreen, SvgViewerScreen)
- **Reusable Components**: Smaller, focused components like FileItem, CloudProviderButton, etc.
- **HOCs**: Higher-Order Components for shared functionality (e.g., withZoomPan)

### State Management
- **Redux** for global state (recent files, user settings, IAP status)
- **Redux Persist** for persisting state across app restarts
- **Local Component State** for UI-specific state

### Navigation
- **React Navigation** with a stack-based approach for the main navigation flow
- Bottom tabs or drawer for secondary navigation (if needed)

### File Handling
- **Factory Pattern** for handling different file types (SVG, SVGZ)
- **Adapter Pattern** for cloud storage providers

### Asynchronous Operations
- **Async/Await** pattern for file operations and API calls
- **Redux Thunk** for asynchronous state updates

## Key Technical Decisions

### Rendering Approach
Two SVG rendering options:
1. **react-native-svg**: Native rendering for performance
2. **WebView**: Fallback for compatibility with complex SVGs

### Gesture Handling
- Using react-native-gesture-handler and react-native-reanimated for smooth zoom/pan interactions

### Layer Control Implementation
- Parsing SVG structure to identify layers (groups)
- Either modifying the SVG string or using dynamic components based on visibility state

### File System Integration
- Using react-native-fs for file operations
- @react-native-documents/picker for file selection
- react-native-receive-sharing-intent for handling "Open with" functionality

### Cloud Integration
- OAuth2 flow for authentication
- Provider-specific APIs for file listing and download
- Local caching of downloaded files

## Component Relationships

```mermaid
graph TD
    App --> AppNavigator
    AppNavigator --> HomeScreen
    AppNavigator --> SvgViewerScreen
    AppNavigator --> SettingsScreen
    
    HomeScreen --> RecentFiles
    HomeScreen --> CloudProviders
    
    SvgViewerScreen --> SvgRenderer
    SvgViewerScreen --> LayerControl
    SvgViewerScreen --> ViewControls
    
    SvgRenderer --> NativeSvgRenderer
    SvgRenderer --> WebViewRenderer
```

## Data Flow

```mermaid
graph LR
    FileSelection --> FileProcessing
    FileProcessing --> Rendering
    Rendering --> UserInteraction
    UserInteraction --> StateUpdate
    StateUpdate --> Rendering
```

## Security Considerations
- Secure storage of OAuth tokens
- Proper file handling to prevent security issues
- Validation of SVG content before rendering 