Okay, this is an excellent and detailed Product Requirements Document (PRD)\! Building this SVG Viewer for Android using React Native without Expo is a solid choice for accessing native capabilities directly.

Let's outline the ultimate tech stack and then break down the development process.

## Ultimate React Native Tech Stack (Non-Expo, TypeScript)

Here are recommended, actively maintained packages:

1.  **Core Framework & Language:**

      * **React Native CLI:** For project initialization and native module linking.
      * **TypeScript:** For static typing, better maintainability, and catching errors early.
      * **React:** The UI library itself.

2.  **Navigation:**

      * **React Navigation (`@react-navigation/native`, `@react-navigation/native-stack`, `@react-navigation/drawer` or `@react-navigation/bottom-tabs` as needed):** The de-facto standard for navigation in React Native. Robust, well-documented, and highly configurable.

3.  **SVG Rendering:**

      * **react-native-svg:** The essential library for rendering SVG files. It provides native SVG rendering capabilities.
      * **react-native-webview:** As an alternative rendering engine option for users.

4.  **File System & Sharing:**

      * **@react-native-documents/picker:** For allowing users to browse and select files from local storage and potentially some cloud providers (via native OS file picker).
      * **react-native-fs:** For more advanced file system operations like caching downloaded cloud files or saving converted PNGs.
      * **react-native-receive-sharing-intent:** To handle files opened via "Open with" or "Share" from other apps.
      * **pako:** A JavaScript zlib/gzip/deflate implementation for handling SVGZ (gzipped SVG) files.

5.  **Cloud Storage Integration:**

      * **Generic API Client:** `axios` or `Workspace` (built-in) for interacting with cloud provider APIs.
      * **Authentication:**
          * **Google Drive:** `  @react-native-google-signin/google-signin ` for OAuth, then use Google Drive API.
          * **Dropbox:** You'll likely implement OAuth 2.0 flow manually (using `react-native-webview` for the auth page or `Linking` for browser redirect) and then use the Dropbox API. There isn't a universally dominant, always-up-to-date SDK, so direct API usage is often more reliable.
          * **OneDrive:** Microsoft Authentication Library (MSAL) for React Native (`react-native-msal`) or manual OAuth 2.0 flow, then use Microsoft Graph API.

6.  **Image Conversion & Capture:**

      * **react-native-view-shot:** To capture the rendered SVG view as a PNG image.

7.  **In-App Purchases:**

      * **react-native-iap:** The most comprehensive library for handling in-app purchases on both iOS and Android.

8.  **State Management:**

      * **Redux Toolkit (with Redux Persist for `recentFiles`):** Scalable and robust for managing global state like user settings, recent files, IAP status, and cloud authentication tokens.
      * **Zustand:** A simpler, more lightweight alternative if you prefer minimal boilerplate. For an app of this complexity, Redux Toolkit might be more beneficial in the long run.

9.  **UI & Styling:**

      * **Styled Components (`styled-components/native`):** For writing CSS-in-JS, creating reusable styled components, and easy theming.
      * **React Native Paper (Optional):** A Material Design component library that can speed up UI development, offering pre-built components like buttons, dialogs, etc.
      * **react-native-vector-icons:** For including icons in your UI.

10. **Gesture Handling (for Zoom/Pan):**

      * **react-native-gesture-handler:** Provides more sophisticated gesture handling than React Native's built-in system.
      * **react-native-reanimated:** For smooth, performant animations, often used in conjunction with `react-native-gesture-handler` for zoom/pan.

11. **Local Storage:**

      * **AsyncStorage (`@react-native-async-storage/async-storage`):** For persisting simple data like user settings or the list of recently opened files. Redux Persist can use this as its storage engine.

12. **Development Utilities:**

      * **ESLint & Prettier:** For code linting and formatting.
      * **Reactotron (Optional):** A desktop app for inspecting your React Native application, including state, API requests, and logs.

## Step-by-Step Development Process (TypeScript Files)

We'll build this app incrementally, focusing on one part at a time. All new component/screen files should be `.tsx` and service/utility files `.ts`.

-----

**Phase 0: Project Setup & Foundation**

1.  **Initialize React Native Project:**
      * Use `npx react-native init SvgViewerApp --template react-native-template-typescript`.
      * Set up ESLint, Prettier, and a `.prettierrc` file for consistent code style.
      * Configure absolute imports for cleaner paths (e.g., `@/components` instead of `../../components`).
2.  **Install Core Dependencies:**
      * Install `react-navigation` (native, native-stack).
      * Install `styled-components`.
      * Install `react-native-vector-icons` and link.
3.  **Basic App Structure:**
      * Create `src` directory.
      * Inside `src`, create directories: `screens`, `components`, `navigation`, `services`, `store` (for Redux), `assets` (for fonts, static images), `types`, `hooks`, `utils`.
4.  **Initial Navigation:**
      * Set up a basic stack navigator in `src/navigation/AppNavigator.tsx`.
      * Create a placeholder `HomeScreen.tsx` and integrate it into the navigator.
      * Modify `App.tsx` to render the `AppNavigator`.

-----

**Phase 1: Core SVG Viewing (Local Files)**

1.  **Install SVG & File Dependencies:**
      * Install `react-native-svg`, `@react-native-documents/picker`, `react-native-fs`, `pako`.
2.  **Create SVG Viewer Screen:**
      * `src/screens/SvgViewerScreen.tsx`: This screen will be responsible for rendering the SVG.
      * Initially, it will take a local file URI as a navigation parameter.
3.  **Implement Local File Picking:**
      * On `HomeScreen.tsx` (or a dedicated "Open File" button/area):
          * Add a button "Open Local SVG".
          * Use `@react-native-documents/picker` to allow the user to select an SVG file.
          * On successful selection, navigate to `SvgViewerScreen.tsx`, passing the file URI.
4.  **Render SVG:**
      * In `SvgViewerScreen.tsx`:
          * Receive the file URI.
          * Use `react-native-fs` to read the file content as a string.
          * Pass this string to the `SvgXml` component from `react-native-svg`.
          * Handle potential errors (file not found, invalid SVG).
5.  **SVGZ Support:**
      * Modify the file reading logic:
          * If the file extension is `.svgz`, read the file as a binary buffer.
          * Use `pako.inflate` to decompress the gzipped content.
          * Convert the decompressed ArrayBuffer to a string before passing to `SvgXml`.
      * File: `src/utils/svgHelper.ts` could contain this logic.

-----

**Phase 2: Enhanced Viewing Features**

1.  **Install Gesture/Animation Dependencies:**
      * Install `react-native-gesture-handler` and `react-native-reanimated`. Ensure `react-native-gesture-handler` is imported at the top of your `index.js` or `App.tsx`.
2.  **Implement Zoom and Pan:**
      * Wrap the `SvgXml` component (or its container view) in `SvgViewerScreen.tsx` with gesture handlers from `react-native-gesture-handler`.
      * Use `useAnimatedStyle` and `useAnimatedGestureHandler` from `react-native-reanimated` to apply scale and translation transforms to the SVG view.
      * This can be complex; consider starting with `react-native-svg`'s `panResponder` props on the `<Svg>` element if simpler, or look for examples of pan/zoom with `react-native-svg` and `reanimated`.
3.  **Full-Screen Mode:**
      * Add a button/icon to toggle full-screen.
      * This could involve:
          * Hiding the app's header bar (React Navigation option).
          * Using a `Modal` component to display the SVG viewer, taking up the whole screen.
          * Using a library like `react-native-bootsplash` to hide status bar/navigation bar if needed, or platform-specific APIs.
4.  **UI for Controls:**
      * Add an overlay for zoom controls (buttons for zoom in/out/reset) and the full-screen toggle if not using gestures for everything.

-----

**Phase 3: Recently Opened Files**

1.  **Install State Management & Storage:**
      * Install `redux` (`@reduxjs/toolkit`), `react-redux`, `@react-native-async-storage/async-storage`, `redux-persist`.
2.  **Setup Redux Store:**
      * Create `src/store/store.ts` to configure the Redux store with `configureStore`.
      * Create `src/store/slices/recentFilesSlice.ts`:
          * Define state: `recentFiles: { path: string, name: string, lastOpened: number }[]`.
          * Define reducers: `addRecentFile`, `clearRecentFiles`.
      * Integrate `redux-persist` to save the `recentFilesSlice` to `AsyncStorage`.
3.  **Update Recent Files Logic:**
      * Whenever an SVG is successfully opened in `SvgViewerScreen.tsx`, dispatch an action to `addRecentFile`. Ensure you handle duplicates and limit the list size (e.g., to 10-20 items).
4.  **Display Recent Files:**
      * On `HomeScreen.tsx` (or a dedicated "Recents" tab/section):
          * Select `recentFiles` from the Redux store.
          * Display them in a list.
          * Tapping an item should navigate to `SvgViewerScreen.tsx` with the file path.

-----

**Phase 4: "Open With" / "Share" Functionality**

1.  **Install Intent Handling Dependency:**
      * Install `react-native-receive-sharing-intent`.
2.  **Configure Native Android Side:**
      * Follow `react-native-receive-sharing-intent` documentation to update `AndroidManifest.xml` and `MainActivity.java` (or `.kt`) to handle `ACTION_SEND` and `ACTION_VIEW` intents for SVG MIME types (`image/svg+xml`).
3.  **Handle Incoming Intents in React Native:**
      * In your main `App.tsx` or a dedicated service `src/services/FileIntentHandler.ts`:
          * Use the library's methods to listen for shared files when the app starts or is already running.
          * When a file is received:
              * Copy the file from its temporary content URI to a more permanent location in your app's cache directory using `react-native-fs` if necessary (content URIs can be short-lived).
              * Navigate directly to `SvgViewerScreen.tsx` with the new file path.
              * Add it to recent files.

-----

**Phase 5: Layer Control (Complex Feature)**

*This is a more advanced feature and requires parsing the SVG XML.*

1.  **SVG Parsing Logic:**
      * When an SVG is loaded, you'll need to parse its XML content (you can use a lightweight JS XML parser if needed, or string manipulation for simpler cases if layers are consistently structured, e.g., `<g id="layer_name">`).
      * Identify elements that represent layers (often `<g>` tags with specific `id`s or Inkscape labels like `inkscape:label`).
      * File: `src/utils/svgParser.ts`.
2.  **State for Layer Visibility:**
      * In `SvgViewerScreen.tsx` or a local component state, maintain an object mapping layer IDs/names to their visibility (e.g., `{ "layer1": true, "layer2": false }`).
3.  **UI for Layer Toggles:**
      * Add a button/panel that, when opened, lists the identified layers with toggles (e.g., `Switch` components).
      * Changing a toggle updates the visibility state.
4.  **Conditional Rendering of SVG Elements (Challenging):**
      * This is the hardest part with `react-native-svg`. You might need to:
          * **Option A (Re-render modified SVG string):** Modify the SVG string itself by adding/removing `display="none"` attributes to layer groups based on visibility state, then re-render the entire `SvgXml`. This is simpler to implement but less performant for frequent changes.
          * **Option B (Dynamic `react-native-svg` components):** If `react-native-svg` allows dynamic construction of its tree from a parsed structure (e.g., rendering `<G>`, `<Path>` components programmatically based on parsed XML and then controlling their `display` prop), this would be more performant. This requires a deeper dive into how `react-native-svg` components can be built from a JS object representation of the SVG. *`react-native-svg` elements do accept a `display="none"` prop, so if you can map your parsed layers to specific `<G>` components (or render them conditionally), this is feasible.*

-----

**Phase 6: Cloud Storage Integration**

*This will be done one provider at a time.*

1.  **General Structure:**
      * `src/services/cloud/GoogleDriveService.ts`, `DropboxService.ts`, `OneDriveService.ts`.
      * Each service will handle:
          * Authentication (OAuth flow).
          * Listing files/folders.
          * Downloading files.
      * Store auth tokens securely (e.g., using `react-native-keychain` or in Redux state if non-sensitive, but keychain is better).
2.  **UI for Cloud Browse:**
      * Add sections/buttons on `HomeScreen.tsx` or a dedicated "Cloud" screen for "Connect to Google Drive," etc.
      * Once connected, provide a simple file browser UI for that cloud service.
      * When an SVG is selected, download it (using `react-native-fs` to save to a cache directory), then navigate to `SvgViewerScreen.tsx`.
3.  **Google Drive:**
      * Integrate `@react-native-google-signin/google-signin`.
      * Implement sign-in flow.
      * Use `Workspace` or `axios` with the access token to call Google Drive API v3 endpoints (e.g., `files.list`, `files.get` with `alt=media`).
4.  **Dropbox:**
      * Implement Dropbox OAuth 2.0 flow (likely using `Linking` to open browser and custom URL scheme to return to app, or a `WebView`).
      * Use `Workspace` or `axios` to call Dropbox API v2 (e.g., `files/list_folder`, `files/download`).
5.  **OneDrive:**
      * Integrate `react-native-msal` or implement Microsoft Graph OAuth 2.0 flow.
      * Use `Workspace` or `axios` to call Microsoft Graph API (e.g., drive items list, download item content).

-----

**Phase 7: PNG Conversion & In-App Purchases**

1.  **Install IAP and ViewShot Dependencies:**
      * Install `react-native-iap`, `react-native-view-shot`.
2.  **Screenshot Functionality (Free):**
      * In `SvgViewerScreen.tsx`, add a "Take Screenshot" button.
      * Use `react-native-view-shot` to capture the view containing the SVG.
      * Use `react-native-fs` to save the image to the device's picture gallery (requires permissions).
      * Inform the user this is "moderate quality."
3.  **Setup In-App Purchases:**
      * Configure products (Silver, Gold) in Google Play Console.
      * Initialize `react-native-iap` in your app (e.g., in `App.tsx` or a dedicated `IapService.ts`).
      * Fetch product details using `getProducts()`.
      * Create a Redux slice `src/store/slices/iapSlice.ts` to store purchase status (e.g., `hasSilver: boolean`, `hasGold: boolean`).
4.  **Implement PNG Conversion (Paid):**
      * In `SvgViewerScreen.tsx`, add a "Convert to PNG" button.
      * When tapped:
          * Check Redux store if Silver or Gold is purchased.
          * If not, show a dialog prompting to purchase, using `requestPurchase()` from `react-native-iap`.
          * If purchased (or after successful purchase):
              * Use `react-native-view-shot` to capture the SVG view.
              * Allow user to choose save location or save to a default app folder.
              * Potentially offer quality/resolution options if `react-native-view-shot` supports it or if you render the SVG at a higher resolution temporarily for capture.
5.  **Restore Purchases:**
      * Implement a "Restore Purchases" button (usually in settings) using `getAvailablePurchases()` from `react-native-iap`.

-----

**Phase 8: Settings Screen**

1.  **Create Settings Screen:**
      * `src/screens/SettingsScreen.tsx`. Add it to your navigator.
2.  **Rendering Engine Choice:**
      * Add a Redux state (e.g., `settingsSlice.ts`) for `renderingEngine: 'native' | 'webview'`. Default to `'native'`.
      * In `SettingsScreen.tsx`, provide a toggle/picker (e.g., Radio buttons) to change this setting.
3.  **Conditional Rendering in `SvgViewerScreen.tsx`:**
      * Based on the `renderingEngine` setting from Redux:
          * If `'native'`, use `react-native-svg` as before.
          * If `'webview'`, use `react-native-webview`. The `source` prop would be ` { html:  `\<body style="margin:0; display:flex; align-items:center; justify-content:center; height:100vh;"\>\<img src="data:image/svg+xml;utf8,${encodeURIComponent(svgString)}" style="width:100%; height:100%; object-fit:contain;" /\>\</body\>`  } ` or similar, injecting the SVG string. Note: WebView pan/zoom is often built-in but might behave differently.

-----

**Phase 9: Finalization & Polish**

1.  **UI/UX Refinement:**
      * Ensure a consistent and intuitive design.
      * Add loading indicators for file operations, cloud interactions, etc.
      * Implement proper error handling and user feedback (toasts, alerts).
      * Empty states for lists (e.g., "No recent files," "No files in this cloud folder").
2.  **Performance Optimization:**
      * Profile the app using Flipper or React DevTools.
      * Optimize heavy computations, especially around SVG parsing/rendering and gestures.
      * Use `React.memo`, `useCallback`, `useMemo` where appropriate.
      * Minimize re-renders.
3.  **Thorough Testing:**
      * Test on various Android versions and devices.
      * Test with Inkscape-generated SVGs of varying complexity.
      * Test all file opening methods (local, "Open With", cloud).
      * Test IAP flow rigorously (including sandbox testing).
      * Test offline functionality.
4.  **Permissions Handling:**
      * Implement robust runtime permission requests for storage access (Android 6+).
5.  **Security:**
      * Ensure cloud API keys/secrets are not hardcoded in the client. Ideally, use a backend proxy for sensitive operations or rely on client-side OAuth where the token is managed by the client. For this app, client-side OAuth is standard for cloud storage.
6.  **App Icons & Splash Screen:**
      * Generate and add adaptive icons for Android.
      * Add a splash screen (e.g., using `react-native-bootsplash`).
7.  **Documentation & Build:**
      * Prepare release notes.
      * Generate a signed APK/AAB for release.

-----

**Best Practices Used:**

  * **TypeScript:** For type safety and better code quality.
  * **Component-Based Architecture:** Breaking UI into reusable components.
  * **State Management:** Using Redux for predictable global state.
  * **Separation of Concerns:** Services for API calls, utils for helper functions.
  * **Native Modules:** Leveraging native capabilities directly where React Native core doesn't suffice.
  * **Incremental Development:** Building features one by one.
  * **User Experience:** Focusing on performance, intuitive UI, and clear feedback.

This detailed plan should guide you through building your SVG Viewer application. Remember to commit frequently and test each feature thoroughly as you build it\! Good luck\!