/**
 * SVG Viewer App
 *
 * @format
 */

import React, {useEffect} from 'react';
import {StatusBar, useColorScheme, Platform} from 'react-native';
import {NavigationContainer, DefaultTheme, DarkTheme} from '@react-navigation/native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
import {store, persistor, AppDispatch} from '@/store';
import AppNavigator from '@/navigation/AppNavigator';
import {useFileIntentHandler} from '@/services/FileIntentHandler';
import {useDispatch} from 'react-redux';
import {addRecentFile} from '@/store/slices/recentFilesSlice';
import {initializeIap, restorePurchases} from '@/store/slices/iapSlice';
import AdService from '@/services/AdService';
import mobileAds from 'react-native-google-mobile-ads';
import IapService from '@/services/IapService';
import {lightColors, darkColors} from '@/theme';
import remoteConfig from '@react-native-firebase/remote-config';

// Configure Google Mobile Ads SDK
const configureAdMob = () => {
  // Set up test device IDs for development
  try {
    mobileAds()
      .initialize()
      .then(adapterStatuses => {
        console.log('Mobile Ads initialization complete:', adapterStatuses);
      })
      .catch(error => {
        console.error('Mobile Ads initialization failed:', error);
      });
  } catch (error) {
    console.error('Error during Mobile Ads setup:', error);
  }
};

// Component to handle file intents and IAP initialization
const AppInitializer = () => {
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize IAP
        await dispatch(initializeIap()).unwrap();

        // Restore any existing purchases
        await dispatch(restorePurchases()).unwrap();

        console.log('IAP initialized and purchases restored');
      } catch (error) {
        console.error('Error initializing IAP:', error);
      }
    };

    // Initialize IAP and restore purchases
    initializeApp();

    // Configure and initialize Google Mobile Ads
    configureAdMob();

    // AdService is self-initializing as a singleton, but we can log it here
    console.log('AdService is ready:', AdService);

    // Cleanup function
    return () => {
      IapService.cleanup();
    };
  }, [dispatch]);
  // Use the file intent handler
  useFileIntentHandler((filePath, fileName) => {
    dispatch(addRecentFile({path: filePath, name: fileName}));
  });

  return null;
};

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';
  const colors = isDarkMode ? darkColors : lightColors;

  const navigationTheme = {
    ...DefaultTheme,
    colors: {
      ...DefaultTheme.colors,
      primary: colors.primary,
      background: colors.background,
      card: colors.surface,
      text: colors.textPrimary,
      border: colors.border,
      notification: colors.primary,
    },
  };
  useEffect(() => {
    remoteConfig()
      .setDefaults({
        ShowAd: true,
      })
      .then(() => remoteConfig().fetchAndActivate())
  }, []);
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <SafeAreaProvider>
          <StatusBar
            barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            backgroundColor={colors.background}
          />
          <NavigationContainer theme={navigationTheme}>
            <AppNavigator />
            <AppInitializer />
          </NavigationContainer>
        </SafeAreaProvider>
      </PersistGate>
    </Provider>
  );
}

export default App;
