{"name": "SvgViewerApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "release": "npx react-native build-android --mode=release", "test-release": "react-native run-android --mode=release", "brt": "npm i && npm run release && npm run test-release", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-documents/picker": "^10.1.3", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/remote-config": "^22.2.1", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@reduxjs/toolkit": "^2.8.2", "@xmldom/xmldom": "^0.8.11", "pako": "^2.1.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-file-access": "^3.1.1", "react-native-gesture-handler": "^2.25.0", "react-native-google-mobile-ads": "^15.2.0", "react-native-iap": "^12.16.2", "react-native-reanimated": "^3.17.5", "react-native-receive-sharing-intent": "^2.0.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/pako": "^2.0.3", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.6.3", "prettier": "^3.5.3", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}