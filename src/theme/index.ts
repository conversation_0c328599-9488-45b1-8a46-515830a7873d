import { useColorScheme } from 'react-native';
import { lightColors, darkColors, Colors } from './colors';
import { typography, Typography } from './typography';
import { spacing, borderRadius, shadows, Spacing, BorderRadius, Shadows } from './spacing';

export interface Theme {
  colors: Colors;
  typography: Typography;
  spacing: Spacing;
  borderRadius: BorderRadius;
  shadows: Shadows;
  isDark: boolean;
}

export const createTheme = (isDark: boolean): Theme => ({
  colors: isDark ? darkColors : lightColors,
  typography,
  spacing,
  borderRadius,
  shadows,
  isDark,
});

export const useTheme = (): Theme => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  return createTheme(isDark);
};

// Export individual theme tokens
export { lightColors, darkColors } from './colors';
export { typography } from './typography';
export { spacing, borderRadius, shadows } from './spacing';

// Export types
export type { Colors, Typography, Spacing, BorderRadius, Shadows };