import React, {useState, useEffect, useMemo} from 'react';
import {StyleProp, ViewStyle, ActivityIndicator, View, Text} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {GestureDetector} from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import ViewShot from 'react-native-view-shot';
import {SvgXml} from 'react-native-svg';

interface NativeSvgRendererProps {
  svgContent: string | null;
  viewShotRef: React.RefObject<ViewShot | null>;
  composedGesture: any;
  animatedStyle: any;
  windowWidth: number;
  windowHeight: number;
  styles: {
    gestureRoot: StyleProp<ViewStyle>;
    svgWrapper: StyleProp<ViewStyle>;
  };
  maxFileSize?: number; // in bytes, default 2MB
  enableChunkedLoading?: boolean;
}

// SVG optimization utilities
const optimizeSvgContent = (svgContent: string): string => {
  return (
    svgContent
      // Remove comments
      .replace(/<!--[\s\S]*?-->/g, '')
      // Remove unnecessary whitespace
      .replace(/\s+/g, ' ')
      // Remove empty elements
      .replace(/<(\w+)([^>]*?)\/>/g, (match, tag, attrs) => {
        if (!attrs.trim()) return '';
        return match;
      })
      // Simplify decimal numbers (reduce precision)
      .replace(/(\d+\.\d{3,})/g, match => {
        return parseFloat(match).toFixed(2);
      })
  );
};

const getSvgFileSize = (svgContent: string): number => {
  return new Blob([svgContent]).size;
};

const NativeSvgRenderer: React.FC<NativeSvgRendererProps> = ({
  svgContent,
  viewShotRef,
  composedGesture,
  animatedStyle,
  windowWidth,
  windowHeight,
  styles,
  maxFileSize = 2 * 1024 * 1024, // 2MB default
  enableChunkedLoading = true,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [optimizedSvg, setOptimizedSvg] = useState<string | null>(null);
  const [renderAttempted, setRenderAttempted] = useState(false);

  // Calculate optimal dimensions based on file size
  const calculateOptimalDimensions = useMemo(() => {
    if (!svgContent) return {width: windowWidth * 0.9, height: windowHeight * 0.6};

    const fileSize = getSvgFileSize(svgContent);
    const sizeFactor = Math.min(1, maxFileSize / fileSize);

    return {
      width: windowWidth * 0.9 * sizeFactor,
      height: windowHeight * 0.6 * sizeFactor,
    };
  }, [svgContent, windowWidth, windowHeight, maxFileSize]);

  // Process SVG content
  useEffect(() => {
    if (!svgContent) {
      setOptimizedSvg(null);
      return;
    }

    const processSvg = async () => {
      setIsLoading(true);
      setError(null);
      setRenderAttempted(false);

      try {
        const fileSize = getSvgFileSize(svgContent);
        console.log(`SVG file size: ${(fileSize / 1024 / 1024).toFixed(2)}MB`);

        if (fileSize > maxFileSize && enableChunkedLoading) {
          // For very large files, try to optimize
          const optimized = optimizeSvgContent(svgContent);
          const optimizedSize = getSvgFileSize(optimized);

          console.log(`Optimized SVG size: ${(optimizedSize / 1024 / 1024).toFixed(2)}MB`);

          if (optimizedSize > maxFileSize) {
            throw new Error(
              `SVG file too large (${(fileSize / 1024 / 1024).toFixed(2)}MB). Maximum supported size is ${(maxFileSize / 1024 / 1024).toFixed(2)}MB, switch to WebView from settings.`,
            );
          }

          // Use setTimeout to prevent blocking the UI thread
          setTimeout(() => {
            setOptimizedSvg(optimized);
            setIsLoading(false);
          }, 100);
        } else {
          // File is within acceptable size, use as is
          setTimeout(() => {
            setOptimizedSvg(svgContent);
            setIsLoading(false);
          }, 50);
        }
      } catch (err) {
        console.error('Error processing SVG:', err);
        setError(err instanceof Error ? err.message : 'Failed to process SVG');
        setIsLoading(false);
      }
    };

    processSvg();
  }, [svgContent, maxFileSize, enableChunkedLoading]);

  // Handle SVG render errors
  const handleSvgError = (error: any) => {
    console.error('SVG render error:', error);
    setError('Failed to render SVG. The file may be corrupted or too complex.');
    setRenderAttempted(true);
  };

  const handleSvgLoad = () => {
    setRenderAttempted(true);
    setError(null);
  };

  // Loading state
  if (isLoading) {
    return (
      <GestureHandlerRootView style={styles.gestureRoot}>
        <View style={[styles.svgWrapper, {justifyContent: 'center', alignItems: 'center'}]}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={{marginTop: 10, color: '#666'}}>Processing SVG...</Text>
        </View>
      </GestureHandlerRootView>
    );
  }

  // Error state
  if (error) {
    return (
      <GestureHandlerRootView style={styles.gestureRoot}>
        <View
          style={[
            styles.svgWrapper,
            {justifyContent: 'center', alignItems: 'center', padding: 20},
          ]}>
          <Text style={{color: '#FF3B30', textAlign: 'center', fontSize: 16, marginBottom: 10}}>
            Unable to Display SVG
          </Text>
          <Text style={{color: '#666', textAlign: 'center', fontSize: 14}}>{error}</Text>
        </View>
      </GestureHandlerRootView>
    );
  }

  // No content state
  if (!optimizedSvg) {
    return (
      <GestureHandlerRootView style={styles.gestureRoot}>
        <View style={[styles.svgWrapper, {justifyContent: 'center', alignItems: 'center'}]}>
          <Text style={{color: '#666'}}>No SVG content</Text>
        </View>
      </GestureHandlerRootView>
    );
  }

  return (
    <GestureHandlerRootView style={styles.gestureRoot}>
      <GestureDetector gesture={composedGesture}>
        <ViewShot ref={viewShotRef} style={styles.gestureRoot}>
          <Animated.View style={[styles.svgWrapper, animatedStyle]}>
            <SvgXml
              xml={optimizedSvg}
              width={calculateOptimalDimensions.width}
              height={calculateOptimalDimensions.height}
              onError={handleSvgError}
              onLoad={handleSvgLoad}
            />
          </Animated.View>
        </ViewShot>
      </GestureDetector>
    </GestureHandlerRootView>
  );
};

export default NativeSvgRenderer;
