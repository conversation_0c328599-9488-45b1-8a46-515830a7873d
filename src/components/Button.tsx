import React from 'react';
import {TouchableOpacity, Text, StyleSheet, GestureResponderEvent, ViewStyle, TextStyle} from 'react-native';
import { useTheme } from '@/theme';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

type ButtonProps = {
  title: string;
  onPress: (event: GestureResponderEvent) => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
};

const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  style,
  textStyle
}: ButtonProps) => {
  const theme = useTheme();
  
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
      justifyContent: 'center',
      ...theme.shadows.sm,
    };

    // Size variations
    const sizeStyles = {
      sm: {
        paddingVertical: theme.spacing[2],
        paddingHorizontal: theme.spacing[3],
      },
      md: {
        paddingVertical: theme.spacing[3],
        paddingHorizontal: theme.spacing[6],
      },
      lg: {
        paddingVertical: theme.spacing[4],
        paddingHorizontal: theme.spacing[8],
      },
    };

    // Variant styles
    const variantStyles = {
      primary: {
        backgroundColor: disabled ? theme.colors.textTertiary : theme.colors.primary,
      },
      secondary: {
        backgroundColor: disabled ? theme.colors.textTertiary : theme.colors.secondary,
      },
      outline: {
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderColor: disabled ? theme.colors.textTertiary : theme.colors.primary,
      },
      ghost: {
        backgroundColor: 'transparent',
        ...theme.shadows.none,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getTextStyle = (): TextStyle => {
    const sizeStyles = {
      sm: {
        fontSize: theme.typography.fontSize.sm,
      },
      md: {
        fontSize: theme.typography.fontSize.base,
      },
      lg: {
        fontSize: theme.typography.fontSize.lg,
      },
    };

    const variantStyles = {
      primary: {
        color: disabled ? theme.colors.background : theme.colors.textInverse,
      },
      secondary: {
        color: disabled ? theme.colors.background : theme.colors.textInverse,
      },
      outline: {
        color: disabled ? theme.colors.textTertiary : theme.colors.primary,
      },
      ghost: {
        color: disabled ? theme.colors.textTertiary : theme.colors.primary,
      },
    };

    return {
      fontWeight: theme.typography.fontWeight.semiBold,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={disabled ? 1 : 0.8}
    >
      <Text style={[getTextStyle(), textStyle]}>{title}</Text>
    </TouchableOpacity>
  );
};

export default Button;
