import React, {useEffect, useMemo, useState} from 'react';
import {Image, Text, View, StyleSheet} from 'react-native';
import remoteConfig from '@react-native-firebase/remote-config';
import {
  NativeAd,
  NativeAdView,
  NativeAsset,
  NativeAssetType,
  NativeMediaView,
  BannerAd,
  BannerAdSize,
  NativeMediaAspectRatio,
} from 'react-native-google-mobile-ads';
import {RootState} from '@/store';
import {useSelector} from 'react-redux';

interface NativeComponentProps {
  nativeId: string;
  bannerId: string;
  showAdMedia?: boolean;
}

export const AdComponent: React.FC<NativeComponentProps> = ({nativeId, bannerId, showAdMedia}) => {
  const [nativeAd, setNativeAd] = useState<NativeAd>();
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const {hasSilver, hasGold} = useSelector((state: RootState) => state.iap);
  // console.log('AdComponent props:', {nativeId, bannerId, showAdMedia, hasSilver, hasGold});
  
  const shouldShowAds = !(hasSilver || hasGold);
  const isBannerAdsOnly = useMemo(
    () => remoteConfig().getValue('showBannerAdsOnly').asBoolean(),
    [],
  );
  const adContainerStyle = useMemo(
    () => [styles.outerContainer, {minHeight: showAdMedia ? 450 : 80}],
    [showAdMedia],
  );

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);

    try {
      NativeAd.createForAdRequest(nativeId, {
        aspectRatio: NativeMediaAspectRatio.SQUARE,
      })
        .then(ad => {
          setNativeAd(ad);
          setHasError(false);
        })
        .catch(error => {
          console.error('Failed to load native ad:', error);
          setHasError(true);
        })
        .finally(() => {
          setIsLoading(false);
        });
    } catch (error) {
      console.error('Error creating native ad request:', error);
      setHasError(true);
      setIsLoading(false);
    }
  }, [nativeId]);
// console.log('AdComponent rendered with nativeId:', nativeId, 'bannerId:', bannerId, 'showAdMedia:', showAdMedia, 'shouldShowAds:', shouldShowAds, 'isBannerAdsOnly:', isBannerAdsOnly);

  // Don't render anything if user has ad-free version
  // if (!shouldShowAds) {
  //   return null;
  // }

  if (isLoading || hasError || !nativeAd || isBannerAdsOnly) {
    return (
      <View style={styles.adOuterContainer}>
        <View style={[styles.outerContainer]}>
          <BannerAd unitId={bannerId} size={BannerAdSize.MEDIUM_RECTANGLE} />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.adOuterContainer}>
      <View style={adContainerStyle}>
        <NativeAdView nativeAd={nativeAd} style={styles.adContainer}>
          <View style={[styles.mainContainer]}>
            <View style={styles.headerContainer}>
              {nativeAd.icon && (
                <NativeAsset assetType={NativeAssetType.ICON}>
                  <Image source={{uri: nativeAd.icon.url}} style={[styles.icon]} />
                </NativeAsset>
              )}
              <View style={styles.titleContainer}>
                <NativeAsset assetType={NativeAssetType.HEADLINE}>
                  <Text style={[styles.headline]}>{nativeAd.headline}</Text>
                </NativeAsset>
                {nativeAd.advertiser && showAdMedia && (
                  <NativeAsset assetType={NativeAssetType.ADVERTISER}>
                    <Text style={styles.advertiser}>{nativeAd.advertiser}</Text>
                  </NativeAsset>
                )}
                {nativeAd.starRating != null && nativeAd.starRating > 0 && showAdMedia && (
                  <NativeAsset assetType={NativeAssetType.STAR_RATING}>
                    <Text style={styles.rating}>Rating: {nativeAd.starRating.toFixed(1)} ★</Text>
                  </NativeAsset>
                )}
              </View>
              <Text style={styles.sponsoredLabel}>Ad</Text>
            </View>

            {showAdMedia && <NativeMediaView style={styles.mediaView} />}

            {showAdMedia && nativeAd.body && (
              <NativeAsset assetType={NativeAssetType.BODY}>
                <Text style={styles.body}>{nativeAd.body}</Text>
              </NativeAsset>
            )}

            {nativeAd.callToAction && (
              <NativeAsset assetType={NativeAssetType.CALL_TO_ACTION}>
                <View style={styles.ctaButton}>
                  <Text style={styles.ctaText}>{nativeAd.callToAction}</Text>
                </View>
              </NativeAsset>
            )}
          </View>
        </NativeAdView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    width: '100%',
    backgroundColor: 'transparent',
  },
  adContainer: {
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e9ecef',
    width: '100%',
    backgroundColor: '#ffffff',
  },
  mainContainer: {
    width: '100%',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginBottom: 12,
    minHeight: 60,
    paddingVertical: 8,
    overflow: 'hidden',
  },
  icon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 16,
  },
  titleContainer: {
    flex: 1,
  },
  headline: {
    fontSize: 18,
    fontWeight: '600',
    color: '#202124',
    marginBottom: 4,
  },
  advertiser: {
    fontSize: 12,
    color: '#adb5bd',
    marginTop: 4,
  },
  sponsoredLabel: {
    fontSize: 12,
    color: '#adb5bd',
    backgroundColor: '#4285F420', // 20% opacity
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
    marginLeft: 8,
  },
  mediaView: {
    width: '100%',
    aspectRatio: 16 / 9,
    maxHeight: 250,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  body: {
    fontSize: 16,
    color: '#adb5bd',
    marginTop: 16,
    marginBottom: 16,
    lineHeight: 22,
    flexShrink: 1,
    minHeight: 60,
  },
  ctaButton: {
    backgroundColor: '#4285F4',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  ctaText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  rating: {
    fontSize: 12,
    color: '#4285F4',
    fontWeight: '500',
    marginTop: 4,
  },
  adOuterContainer: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 24,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
});
