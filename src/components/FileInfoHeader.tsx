import React from 'react';
import { View, Text, StyleProp, ViewStyle, TextStyle } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface FileInfoHeaderProps {
  fileName: string;
  fileInfo: {
    size: string;
    lastModified: string;
    isCompressed: boolean;
  } | null;
  styles: {
    header: StyleProp<ViewStyle>;
    headerIcon?: StyleProp<TextStyle>;
    headerText: StyleProp<TextStyle>;
    fileInfoContainer: StyleProp<ViewStyle>;
    fileInfoText: StyleProp<TextStyle>;
  };
}

const FileInfoHeader: React.FC<FileInfoHeaderProps> = ({
  fileName,
  fileInfo,
  styles
}) => {
  return (
    <>
      <View style={styles.header}>
        <Icon name="insert-photo" size={24} color="#007AFF" style={styles.headerIcon} />
        <Text style={styles.headerText}>{fileName}</Text>
      </View>

      {fileInfo && (
        <View style={styles.fileInfoContainer}>
          <Text style={styles.fileInfoText}>Size: {fileInfo.size}</Text>
          <Text style={styles.fileInfoText}>Modified: {fileInfo.lastModified}</Text>
          {fileInfo.isCompressed && (
            <Text style={styles.fileInfoText}>Format: Compressed SVGZ</Text>
          )}
        </View>
      )}
    </>
  );
};

export default FileInfoHeader;
