import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Switch, StyleProp, ViewStyle, TextStyle } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SvgLayer } from '@/utils/svgParser';

interface LayerPanelProps {
  layers: SvgLayer[];
  onClose: () => void;
  onToggleLayerVisibility: (layerId: string, visible: boolean) => void;
  styles: {
    layerPanel: StyleProp<ViewStyle>;
    layerPanelHeader: StyleProp<ViewStyle>;
    layerPanelTitle: StyleProp<TextStyle>;
    layerList: StyleProp<ViewStyle>;
    layerItem: StyleProp<ViewStyle>;
    layerName: StyleProp<TextStyle>;
  };
}

const LayerPanel: React.FC<LayerPanelProps> = ({
  layers,
  onClose,
  onToggleLayerVisibility,
  styles
}) => {
  return (
    <View style={styles.layerPanel}>
      <View style={styles.layerPanelHeader}>
        <Text style={styles.layerPanelTitle}>Layers</Text>
        <TouchableOpacity onPress={onClose}>
          <Icon name="close" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>
      <ScrollView style={styles.layerList}>
        {layers.map((layer) => (
          <View key={layer.id} style={styles.layerItem}>
            <Text style={styles.layerName} numberOfLines={1} ellipsizeMode="tail">
              {layer.name}
            </Text>
            <Switch
              value={layer.visible}
              onValueChange={(value) => onToggleLayerVisibility(layer.id, value)}
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={layer.visible ? '#007AFF' : '#f4f3f4'}
            />
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default LayerPanel;
