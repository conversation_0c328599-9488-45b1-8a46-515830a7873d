import React, { useRef, useCallback, forwardRef, useImperativeHandle } from 'react';
import { View, Dimensions } from 'react-native';
import ViewShot from 'react-native-view-shot';
import { WebView } from 'react-native-webview';

interface WebViewSvgRendererProps {
  svgContent: string | null;
  isFullscreen: boolean;
  viewShotRef: React.RefObject<ViewShot | null>;
}

export interface WebViewSvgRendererRef {
  zoomIn: () => void;
  zoomOut: () => void;
  resetView: () => void;
  rotateLeft: () => void;
  rotateRight: () => void;
}

const WebViewSvgRenderer = forwardRef<WebViewSvgRendererRef, WebViewSvgRendererProps>(({
  svgContent,
  isFullscreen,
  viewShotRef
}, ref) => {
  const windowWidth = Dimensions.get('window').width;
  const windowHeight = Dimensions.get('window').height;
  const webViewRef = useRef<WebView>(null);

  // Zoom control functions that communicate with WebView
  const handleZoomIn = useCallback(() => {
    console.log('handleZoomIn called');
    webViewRef.current?.postMessage(JSON.stringify({ action: 'zoomIn' }));
  }, []);

  const handleZoomOut = useCallback(() => {
    console.log('handleZoomOut called');
    webViewRef.current?.postMessage(JSON.stringify({ action: 'zoomOut' }));
  }, []);

  const handleResetView = useCallback(() => {
    console.log('handleResetView called');
    webViewRef.current?.postMessage(JSON.stringify({ action: 'resetView' }));
  }, []);

  const handleRotateLeft = useCallback(() => {
    console.log('handleRotateLeft called');
    webViewRef.current?.postMessage(JSON.stringify({ action: 'rotateLeft' }));
  }, []);

  const handleRotateRight = useCallback(() => {
    console.log('handleRotateRight called');
    webViewRef.current?.postMessage(JSON.stringify({ action: 'rotateRight' }));
  }, []);

  // Expose zoom and rotation functions to parent component
  useImperativeHandle(ref, () => ({
    zoomIn: handleZoomIn,
    zoomOut: handleZoomOut,
    resetView: handleResetView,
    rotateLeft: handleRotateLeft,
    rotateRight: handleRotateRight
  }), [handleZoomIn, handleZoomOut, handleResetView, handleRotateLeft, handleRotateRight]);

  return (
    <ViewShot ref={viewShotRef}>
      <View style={{
        width: windowWidth,
        height: windowHeight * (isFullscreen ? 0.8 : 0.6),
        backgroundColor: isFullscreen ? '#000' : '#fff'
      }}>
        <WebView
          ref={webViewRef}
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: 'transparent',
            opacity: 1
          }}
          originWhitelist={['*']}
          source={{
            html: `
              <!DOCTYPE html>
              <html>
                <head>
                  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
                  <style>
                    html, body {
                      margin: 0;
                      padding: 0;
                      width: 100%;
                      height: 100%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      background-color: ${isFullscreen ? '#000' : '#fff'};
                      overflow: auto;
                    }
                    #svg-container {
                      width: 100%;
                      height: 100%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      transform-origin: center center;
                      transition: transform 0.3s ease;
                    }
                    svg {
                      max-width: 100%;
                      max-height: 100%;
                      width: auto;
                      height: auto;
                    }
                    .debug {
                      position: absolute;
                      top: 10px;
                      left: 10px;
                      background: rgba(0,0,0,0.7);
                      color: white;
                      padding: 5px;
                      font-size: 12px;
                      z-index: 1000;
                    }
                  </style>
                </head>
                <body>
                  <div class="debug" id="debug">Ready</div>
                  <div id="svg-container">
                    ${svgContent || '<p style="color: red; text-align: center;">No SVG content</p>'}
                  </div>
                  <script>
                    var currentScale = 1;
                    var currentTranslateX = 0;
                    var currentTranslateY = 0;
                    var currentRotation = 0;
                    var container = document.getElementById('svg-container');
                    var debug = document.getElementById('debug');
                    
                    function updateDebug() {
                      var svgElement = document.querySelector('svg');
                      debug.innerHTML = 'Scale: ' + currentScale.toFixed(2) + ', Rotation: ' + currentRotation + '°, SVG: ' + (svgElement ? 'Found' : 'Not found');
                    }
                    
                    function updateTransform() {
                      container.style.transform = 'translate(' + currentTranslateX + 'px, ' + currentTranslateY + 'px) scale(' + currentScale + ') rotate(' + currentRotation + 'deg)';
                      updateDebug();
                    }
                    
                    function zoomIn() {
                      currentScale = Math.min(currentScale * 1.5, 10);
                      updateTransform();
                    }
                    
                    function zoomOut() {
                      currentScale = Math.max(currentScale / 1.5, 0.1);
                      updateTransform();
                    }
                    
                    function resetView() {
                      currentScale = 1;
                      currentTranslateX = 0;
                      currentTranslateY = 0;
                      currentRotation = 0;
                      updateTransform();
                    }
                    
                    function rotateLeft() {
                      currentRotation = (currentRotation - 90);
                      updateTransform();
                    }
                    
                    function rotateRight() {
                      currentRotation = (currentRotation + 90);
                      updateTransform();
                    }
                    
                    // Initialize
                    updateDebug();
                    
                    // Handle messages from React Native
                    window.addEventListener('message', function(event) {
                      try {
                        var data = JSON.parse(event.data);
                        debug.innerHTML = 'Scale: ' + currentScale.toFixed(2) + ', Message: ' + data.action;
                        switch(data.action) {
                          case 'zoomIn':
                            zoomIn();
                            break;
                          case 'zoomOut':
                            zoomOut();
                            break;
                          case 'resetView':
                            resetView();
                            break;
                          case 'rotateLeft':
                            rotateLeft();
                            break;
                          case 'rotateRight':
                            rotateRight();
                            break;
                        }
                      } catch (e) {
                        console.log('Error:', e);
                        debug.innerHTML = 'Scale: ' + currentScale.toFixed(2) + ', Error: ' + e.message;
                      }
                    });
                    
                    // Also try document message listener for compatibility
                    document.addEventListener('message', function(event) {
                      try {
                        var data = JSON.parse(event.data);
                        debug.innerHTML = 'Scale: ' + currentScale.toFixed(2);
                        switch(data.action) {
                          case 'zoomIn':
                            zoomIn();
                            break;
                          case 'zoomOut':
                            zoomOut();
                            break;
                          case 'resetView':
                            resetView();
                            break;
                          case 'rotateLeft':
                            rotateLeft();
                            break;
                          case 'rotateRight':
                            rotateRight();
                            break;
                        }
                      } catch (e) {
                        console.log('Doc Error:', e);
                      }
                    });
                    
                    // Touch handling variables
                    var initialDistance = 0;
                    var initialScale = 1;
                    var initialTouchX = 0;
                    var initialTouchY = 0;
                    var initialTranslateX = 0;
                    var initialTranslateY = 0;
                    var initialRotation = 0;
                    var initialAngle = 0;
                    var isPinching = false;
                    var isPanning = false;
                    var isRotating = false;
                    var touchStartTime = 0;
                    var lastTapTime = 0;
                    
                    function getDistance(touch1, touch2) {
                      return Math.sqrt(
                        Math.pow(touch2.clientX - touch1.clientX, 2) +
                        Math.pow(touch2.clientY - touch1.clientY, 2)
                      );
                    }
                    
                    function getCenterPoint(touch1, touch2) {
                      return {
                        x: (touch1.clientX + touch2.clientX) / 2,
                        y: (touch1.clientY + touch2.clientY) / 2
                      };
                    }
                    
                    function getAngle(touch1, touch2) {
                      return Math.atan2(touch2.clientY - touch1.clientY, touch2.clientX - touch1.clientX) * 180 / Math.PI;
                    }
                    
                    container.addEventListener('touchstart', function(e) {
                      touchStartTime = Date.now();
                      
                      if (e.touches.length === 2) {
                        // Two finger pinch and rotate
                        e.preventDefault();
                        isPinching = true;
                        isRotating = true;
                        isPanning = false;
                        initialDistance = getDistance(e.touches[0], e.touches[1]);
                        initialScale = currentScale;
                        initialAngle = getAngle(e.touches[0], e.touches[1]);
                        initialRotation = currentRotation;
                      } else if (e.touches.length === 1 && !isPinching) {
                        // Single finger pan (only if not already pinching)
                        isPanning = currentScale > 1; // Only allow panning when zoomed
                        isPinching = false;
                        initialTouchX = e.touches[0].clientX;
                        initialTouchY = e.touches[0].clientY;
                        initialTranslateX = currentTranslateX;
                        initialTranslateY = currentTranslateY;
                      }
                    });
                    
                    container.addEventListener('touchmove', function(e) {
                      if (isPinching && e.touches.length === 2) {
                        e.preventDefault();
                        var currentDistance = getDistance(e.touches[0], e.touches[1]);
                        var currentAngle = getAngle(e.touches[0], e.touches[1]);
                        
                        // Handle scaling
                        if (initialDistance > 0) {
                          var scaleChange = currentDistance / initialDistance;
                          var newScale = initialScale * scaleChange;
                          currentScale = Math.min(Math.max(newScale, 0.1), 10);
                        }
                        
                        // Handle rotation
                        if (isRotating) {
                          var angleDiff = currentAngle - initialAngle;
                          currentRotation = initialRotation + angleDiff;
                          // Normalize rotation to 0-360 degrees
                          // currentRotation = ((currentRotation) + 360);
                        }
                        
                        updateTransform();
                      } else if (isPanning && e.touches.length === 1 && currentScale > 1) {
                        e.preventDefault();
                        var deltaX = e.touches[0].clientX - initialTouchX;
                        var deltaY = e.touches[0].clientY - initialTouchY;
                        currentTranslateX = initialTranslateX + deltaX;
                        currentTranslateY = initialTranslateY + deltaY;
                        updateTransform();
                      }
                    }, { passive: false });
                    
                    container.addEventListener('touchend', function(e) {
                      var touchEndTime = Date.now();
                      var touchDuration = touchEndTime - touchStartTime;
                      
                      // Reset states when no touches remain
                      if (e.touches.length === 0) {
                        // Check for double tap (quick tap, short duration)
                        if (touchDuration < 300 && !isPinching && !isPanning) {
                          var timeSinceLastTap = touchEndTime - lastTapTime;
                          if (timeSinceLastTap < 500 && timeSinceLastTap > 50) {
                            // Double tap detected
                            if (currentScale > 1.5) {
                              resetView();
                            } else {
                              currentScale = 2.5;
                              updateTransform();
                            }
                          }
                          lastTapTime = touchEndTime;
                        }
                        
                        isPinching = false;
                        isPanning = false;
                        isRotating = false;
                      } else if (e.touches.length === 1 && isPinching) {
                        // Transition from pinch to potential pan
                        isPinching = false;
                        isRotating = false;
                        isPanning = currentScale > 1;
                        if (isPanning) {
                          initialTouchX = e.touches[0].clientX;
                          initialTouchY = e.touches[0].clientY;
                          initialTranslateX = currentTranslateX;
                          initialTranslateY = currentTranslateY;
                        }
                      }
                    });
                    
                    // Prevent context menu on long press
                    container.addEventListener('contextmenu', function(e) {
                      e.preventDefault();
                    });
                  </script>
                </body>
              </html>
            `
          }}
          onMessage={(event) => {
            // Handle messages from WebView if needed
            console.log('WebView message:', event.nativeEvent.data);
          }}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.warn('WebView error: ', nativeEvent);
          }}
          onLoadStart={() => {
            console.log('WebView load started');
          }}
          onLoadEnd={() => {
            console.log('WebView SVG load complete');
            // Send a test message to verify communication
            setTimeout(() => {
              webViewRef.current?.postMessage(JSON.stringify({ action: 'test' }));
            }, 1000);
          }}
          onLoadProgress={({ nativeEvent }) => {
            console.log('WebView load progress:', nativeEvent.progress);
          }}
          onHttpError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.warn('WebView HTTP error:', nativeEvent);
          }}
          onRenderProcessGone={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.warn('WebView render process gone:', nativeEvent);
          }}
          scrollEnabled={false}
          bounces={false}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          javaScriptEnabled={true}
          domStorageEnabled={true}
        />
      </View>
    </ViewShot>
  );
});

WebViewSvgRenderer.displayName = 'WebViewSvgRenderer';

export default WebViewSvgRenderer;
