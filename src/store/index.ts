import {configureStore} from '@reduxjs/toolkit';
import {persistStore, persistReducer} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import recentFilesReducer from './slices/recentFilesSlice';
import iapReducer from './slices/iapSlice';
import settingsReducer from './slices/settingsSlice';

// Configure persist for recent files
const recentFilesPersistConfig = {
  key: 'recentFiles',
  storage: AsyncStorage,
};

// Configure persist for IAP state
const iapPersistConfig = {
  key: 'iap',
  storage: AsyncStorage,
};

// Configure persist for settings
const settingsPersistConfig = {
  key: 'settings',
  storage: AsyncStorage,
};

// Create the store with middleware
export const store = configureStore({
  reducer: {
    recentFiles: persistReducer(recentFilesPersistConfig, recentFilesReducer),
    iap: persistReducer(iapPersistConfig, iapReducer),
    settings: persistReducer(settingsPersistConfig, settingsReducer),
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

// Create the persistor
export const persistor = persistStore(store);

// Define RootState type
export type RootState = ReturnType<typeof store.getState>;

// Define AppDispatch type
export type AppDispatch = typeof store.dispatch;