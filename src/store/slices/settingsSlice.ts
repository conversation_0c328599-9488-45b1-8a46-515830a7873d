import {createSlice, PayloadAction} from '@reduxjs/toolkit';

export type RenderingEngine = 'native' | 'webview';

interface SettingsState {
  renderingEngine: RenderingEngine;
}

const initialState: SettingsState = {
  renderingEngine: 'webview',
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setRenderingEngine: (state, action: PayloadAction<RenderingEngine>) => {
      state.renderingEngine = action.payload;
    },
  },
});

export const {setRenderingEngine} = settingsSlice.actions;
export default settingsSlice.reducer;