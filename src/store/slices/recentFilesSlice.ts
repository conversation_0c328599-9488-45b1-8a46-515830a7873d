import {createSlice, PayloadAction} from '@reduxjs/toolkit';

type RecentFile = {
  path: string;
  name: string;
  lastOpened: number;
};

type RecentFilesState = {
  files: RecentFile[];
};

const initialState: RecentFilesState = {
  files: [],
};

const recentFilesSlice = createSlice({
  name: 'recentFiles',
  initialState,
  reducers: {
    addRecentFile: (state, action: PayloadAction<{path: string; name: string}>) => {
      const {path, name} = action.payload;
      
      // Check if file already exists in recent files
      const existingIndex = state.files.findIndex(file => file.path === path);
      
      // If exists, remove it so we can add it to the top
      if (existingIndex !== -1) {
        state.files.splice(existingIndex, 1);
      }
      
      // Add file to the beginning of the array
      state.files.unshift({
        path,
        name,
        lastOpened: Date.now(),
      });
      
      // Limit the number of recent files to 20
      if (state.files.length > 20) {
        state.files = state.files.slice(0, 20);
      }
    },
    clearRecentFiles: (state) => {
      state.files = [];
    },
  },
});

export const {addRecentFile, clearRecentFiles} = recentFilesSlice.actions;
export default recentFilesSlice.reducer;