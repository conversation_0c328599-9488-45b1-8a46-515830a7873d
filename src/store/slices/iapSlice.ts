import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import IapService, {productIds} from '@/services/IapService';
import type {Purchase} from 'react-native-iap';

interface IapState {
  hasSilver: boolean;
  hasGold: boolean;
  isLoading: boolean;
  error: string | null;
}

const initialState: IapState = {
  hasSilver: false,
  hasGold: false,
  isLoading: false,
  error: null,
};

export const initializeIap = createAsyncThunk('iap/initialize', async () => {
  await IapService.initialize();
});

export const restorePurchases = createAsyncThunk('iap/restorePurchases', async () => {
  const purchases = await IapService.restorePurchases();
  return processPurchases(purchases);
});

export const processPurchaseCompletion = createAsyncThunk(
  'iap/processPurchaseCompletion',
  async (purchase: Purchase) => {
    // Process the single purchase and update status
    return processPurchases([purchase]);
  },
);

// Helper function to process purchases and determine tier status
const processPurchases = (purchases: Purchase[]) => {
  const status = {
    hasSilver: false,
    hasGold: false,
  };

  purchases.forEach(purchase => {
    if (purchase.productId === productIds.silver) {
      status.hasSilver = true;
    }
    if (purchase.productId === productIds.gold) {
      status.hasGold = true;
    }
  });

  return status;
};

const iapSlice = createSlice({
  name: 'iap',
  initialState,
  reducers: {
    setPurchaseStatus: (state, action) => {
      state.hasSilver = action.payload.hasSilver;
      state.hasGold = action.payload.hasGold;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(initializeIap.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(initializeIap.fulfilled, state => {
        state.isLoading = false;
      })
      .addCase(initializeIap.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to initialize IAP';
      })
      .addCase(restorePurchases.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(restorePurchases.fulfilled, (state, action) => {
        state.isLoading = false;
        state.hasSilver = action.payload.hasSilver;
        state.hasGold = action.payload.hasGold;
      })
      .addCase(restorePurchases.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to restore purchases';
      })
      .addCase(processPurchaseCompletion.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(processPurchaseCompletion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.hasSilver = action.payload.hasSilver;
        state.hasGold = action.payload.hasGold;
      })
      .addCase(processPurchaseCompletion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to process purchase';
      });
  },
});

export const {setPurchaseStatus} = iapSlice.actions;
export default iapSlice.reducer;
