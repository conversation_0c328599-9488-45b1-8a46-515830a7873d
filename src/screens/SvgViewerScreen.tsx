import React, {useState, useCallback, useRef, useMemo, useEffect} from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
  Modal,
  StatusBar,
  SafeAreaView,
  Platform,
} from 'react-native';
import BannerAd from '@/components/BannerAd';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '@/navigation/AppNavigator';
import ViewShot from 'react-native-view-shot';
import {useSelector} from 'react-redux';
import type {RootState} from '@/store';
import {useTheme} from '@/theme';

// Custom hooks
import {useSvgGestures} from '@/hooks/useSvgGestures';
import {useSvgLoader} from '@/hooks/useSvgLoader';

// Components
import WebViewSvgRenderer, {WebViewSvgRendererRef} from '@/components/WebViewSvgRenderer';
import NativeSvgRenderer from '@/components/NativeSvgRenderer';
import LayerPanel from '@/components/LayerPanel';
import SvgControls from '@/components/SvgControls';
import FileInfoHeader from '@/components/FileInfoHeader';

// Utilities
import {showPurchaseDialog} from '@/utils/purchaseDialogUtils';
import {savePng} from '@/utils/pngConversionUtils';
import AdService from '@/services/AdService';

type SvgViewerScreenProps = NativeStackScreenProps<RootStackParamList, 'SvgViewer'>;


const SvgViewerScreen = ({route, navigation}: SvgViewerScreenProps) => {
  const {uri, fileName} = route.params;
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [controlsVisible, setControlsVisible] = useState(true);
  const [layerPanelVisible, setLayerPanelVisible] = useState(false);
  const viewShotRef = useRef<ViewShot>(null);
  const webViewRef = useRef<WebViewSvgRendererRef>(null);
  const {hasSilver, hasGold} = useSelector((state: RootState) => state.iap);
  const {renderingEngine} = useSelector((state: RootState) => state.settings);
  const theme = useTheme();

  const windowWidth = Dimensions.get('window').width;
  const windowHeight = Dimensions.get('window').height;

  // Toggle controls visibility
  const toggleControls = useCallback(() => {
    setControlsVisible(!controlsVisible);
    if (controlsVisible && layerPanelVisible) {
      setLayerPanelVisible(false);
    }
  }, [controlsVisible, layerPanelVisible]);

  // Toggle fullscreen mode
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // Update navigation options when fullscreen changes
  useEffect(() => {
    navigation.setOptions({
      headerShown: !isFullscreen,
    });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFullscreen]);

  // Toggle layer panel visibility
  const toggleLayerPanel = useCallback(() => {
    if (!hasGold && !layerPanelVisible) {
      showPurchaseDialog(
        'to unlock this feature, please purchase Gold subscription to unlocks everything and remove ads',
        () => setLayerPanelVisible(!layerPanelVisible),
        false,
      );
      return;
    }
    setLayerPanelVisible(!layerPanelVisible);
  }, [layerPanelVisible, hasGold]);

  // Use custom hooks
  const {svgContent, fileInfo, loading, error, layers, toggleLayerVisibility} = useSvgLoader(uri);

  const {resetView, zoomIn, zoomOut, rotateLeft, rotateRight, composedGesture, animatedStyle} =
    useSvgGestures(toggleControls);

  // Handle PNG conversion
  const handleSavePng = useCallback(async () => {
    if (!viewShotRef.current) return;
    return savePng(viewShotRef, fileName);
  }, [viewShotRef, fileName]);

  // Convert to PNG with premium check
  const convertToPng = useCallback(() => {
    if (!viewShotRef.current) return;

    if (!hasSilver && !hasGold) {
      showPurchaseDialog(
        'to unlock this feature, please purchase Silver subscription to unlocks PNG conversion and remove ads or Gold subscription to unlocks everything and remove ads',
        handleSavePng,
      );
      return;
    }

    handleSavePng();
  }, [hasSilver, hasGold, handleSavePng]);

  // Create styles using theme
  const styles = createStyles(theme);

  // Create zoom and rotation functions for WebView
  const webViewZoomIn = useCallback(() => {
    webViewRef.current?.zoomIn();
  }, []);

  const webViewZoomOut = useCallback(() => {
    webViewRef.current?.zoomOut();
  }, []);

  const webViewResetView = useCallback(() => {
    webViewRef.current?.resetView();
  }, []);

  const webViewRotateLeft = useCallback(() => {
    webViewRef.current?.rotateLeft();
  }, []);

  const webViewRotateRight = useCallback(() => {
    webViewRef.current?.rotateRight();
  }, []);

  // Memoize the SVG rendering function to prevent unnecessary re-renders
  const renderSvgContent = () => {
    if (renderingEngine === 'webview') {
      return (
        <WebViewSvgRenderer
          ref={webViewRef}
          viewShotRef={viewShotRef}
          svgContent={svgContent}
          isFullscreen={isFullscreen}
        />
      );
    }

    return (
      <NativeSvgRenderer
        svgContent={svgContent}
        svgUri={uri}
        viewShotRef={viewShotRef}
        composedGesture={composedGesture}
        animatedStyle={animatedStyle}
        windowWidth={windowWidth}
        windowHeight={windowHeight}
        styles={{
          gestureRoot: {...styles.gestureRoot, backgroundColor: isFullscreen ? 'black' : 'white'},
          svgWrapper: styles.svgWrapper,
        }}
      />
    );
  };

  console.log('renderSvgContent', layers.length);
  const renderContent = () => (
    <View style={[styles.container, isFullscreen && styles.fullscreen]}>
      {!isFullscreen && (
        <FileInfoHeader
          fileName={fileName}
          fileInfo={fileInfo}
          styles={{
            header: styles.header,
            headerIcon: styles.headerIcon,
            headerText: styles.headerText,
            fileInfoContainer: styles.fileInfoContainer,
            fileInfoText: styles.fileInfoText,
          }}
        />
      )}

      <View style={[styles.svgContainer, isFullscreen && styles.fullscreenSvgContainer]}>
        {loading && <ActivityIndicator size="large" color="#007AFF" />}
        {error && <Text style={styles.error}>{error}</Text>}
        {svgContent && !error && !loading && renderSvgContent()}

        {!loading && !error && svgContent && controlsVisible && (
          <SvgControls
            onConvertToPng={convertToPng}
            onZoomIn={renderingEngine === 'webview' ? webViewZoomIn : zoomIn}
            onZoomOut={renderingEngine === 'webview' ? webViewZoomOut : zoomOut}
            onResetView={renderingEngine === 'webview' ? webViewResetView : resetView}
            onRotateLeft={renderingEngine === 'webview' ? webViewRotateLeft : rotateLeft}
            onRotateRight={renderingEngine === 'webview' ? webViewRotateRight : rotateRight}
            onToggleFullscreen={toggleFullscreen}
            onToggleLayerPanel={toggleLayerPanel}
            isFullscreen={isFullscreen}
            isWebViewEngine={false}
            layerPanelVisible={layerPanelVisible}
            hasLayers={layers.length > 0}
            styles={{
              controls: styles.controls,
              controlButton: styles.controlButton,
              activeControlButton: styles.activeControlButton,
            }}
          />
        )}

        {!loading && !error && svgContent && layerPanelVisible && (
          <LayerPanel
            layers={layers}
            onClose={toggleLayerPanel}
            onToggleLayerVisibility={toggleLayerVisibility}
            styles={{
              layerPanel: styles.layerPanel,
              layerPanelHeader: styles.layerPanelHeader,
              layerPanelTitle: styles.layerPanelTitle,
              layerList: styles.layerList,
              layerItem: styles.layerItem,
              layerName: styles.layerName,
            }}
          />
        )}
      </View>

      {!isFullscreen && !hasGold && !hasSilver && (
        <View style={styles.adContainer}>
          <BannerAd
            nativeId={AdService.getNativeAdUnitId()}
            bannerId={AdService.getBannerAdUnitId()}
          />
        </View>
      )}
    </View>
  );

  return (
    <>
      <Modal
        visible={isFullscreen}
        animationType="fade"
        transparent={false}
        onRequestClose={toggleFullscreen}>
        <SafeAreaView style={styles.container}>
          <StatusBar hidden={Platform.OS === 'ios'} />
          <View style={styles.fullscreenAdContainer}>
            <BannerAd
              nativeId={AdService.getNativeAdUnitId()}
              bannerId={AdService.getBannerAdUnitId()}
            />
          </View>
          {renderContent()}
        </SafeAreaView>
      </Modal>

      {!isFullscreen && renderContent()}
    </>
  );
};
const createStyles = (theme: any) =>
  StyleSheet.create({
    mainContent: {
      flex: 1,
    },
    adContainer: {
      width: '100%',
      marginBottom: 0,
    },
    fullscreenAdContainer: {
      width: '100%',
      paddingTop: Platform.OS === 'ios' ? 20 : 0,
      backgroundColor: theme.colors.surface,
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    fullscreen: {
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: theme.spacing[4],
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    headerIcon: {
      marginRight: theme.spacing[2],
    },
    headerText: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semiBold,
      color: theme.colors.textPrimary,
      flex: 1,
    },
    fileInfoContainer: {
      padding: theme.spacing[4],
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    fileInfoText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing[1],
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.sm,
    },
    svgContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing[4],
      position: 'relative',
    },
    fullscreenSvgContainer: {
      padding: 0,
      backgroundColor: theme.colors.background,
    },
    gestureRoot: {
      flex: 1,
      width: '100%',
      height: '100%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    svgWrapper: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    controls: {
      position: 'absolute',
      bottom: theme.spacing[8],
      right: theme.spacing[4],
      flexDirection: 'row',
      backgroundColor: theme.colors.surfaceElevated + 'F0',
      borderRadius: theme.borderRadius.xl,
      padding: theme.spacing[2],
      ...theme.shadows.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    controlButton: {
      width: 44,
      height: 44,
      borderRadius: theme.borderRadius.full,
      backgroundColor: theme.colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: theme.spacing[1],
      ...theme.shadows.base,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    error: {
      color: theme.colors.error,
      fontSize: theme.typography.fontSize.base,
      textAlign: 'center',
      padding: theme.spacing[5],
      fontWeight: theme.typography.fontWeight.medium,
    },
    activeControlButton: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    layerPanel: {
      position: 'absolute',
      bottom: 80,
      right: theme.spacing[4],
      width: 280,
      maxHeight: 320,
      backgroundColor: theme.colors.surfaceElevated,
      borderRadius: theme.borderRadius.xl,
      padding: theme.spacing[4],
      ...theme.shadows.xl,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    layerPanelHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing[3],
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      paddingBottom: theme.spacing[2],
    },
    layerPanelTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semiBold,
      color: theme.colors.textPrimary,
    },
    layerList: {
      maxHeight: 240,
    },
    layerItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: theme.spacing[2],
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.borderLight,
    },
    layerName: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textPrimary,
      flex: 1,
      marginRight: theme.spacing[2],
    },
  });
export default SvgViewerScreen;
