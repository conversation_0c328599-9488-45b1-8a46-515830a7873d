import React, {useState} from 'react';
import {View, Text, StyleSheet, Alert, ActivityIndicator, ScrollView} from 'react-native';
import BannerAd from '@/components/BannerAd';
import Button from '@/components/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '@/navigation/AppNavigator';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {isErrorWithCode, pick} from '@react-native-documents/picker';
import {useTheme} from '@/theme';
import AdService from '@/services/AdService';
import {AdComponent} from '@/components/AdComponent';

type HomeScreenProps = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen = ({navigation}: HomeScreenProps) => {
  const [isPickerActive, setIsPickerActive] = useState(false);
  const theme = useTheme();

  const handlePickDocument = async () => {
    try {
      setIsPickerActive(true);

      const pickerResult = await pick({
        type: ['image/svg+xml', '.svg', '.svgz'],
        allowMultiSelection: false,
        allowVirtualFiles: true,
      });

      // DocumentPicker returns an array, but we're only allowing single selection
      const file = pickerResult[0];

      // Navigate to the SVG viewer with the selected file URI
      navigation.navigate('SvgViewer', {uri: file.uri, fileName: file.name ?? 'untitled.svg'});
    } catch (e) {
      // Handle errors
      if (isErrorWithCode(e)) {
        // Picker was canceled by user
        console.log('Document picker canceled');
      } else {
        console.error('Error picking document:', e);
        Alert.alert('Error', 'There was a problem selecting the file. Please try again.', [
          {text: 'OK'},
        ]);
      }
    } finally {
      setIsPickerActive(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
    },
    contentContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: theme.spacing[6],
      paddingVertical: theme.spacing[8],
    },
    heroSection: {
      alignItems: 'center',
      marginBottom: theme.spacing[12],
    },
    iconContainer: {
      width: 120,
      height: 120,
      borderRadius: theme.borderRadius['2xl'],
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: theme.spacing[6],
      ...theme.shadows.lg,
    },
    title: {
      fontSize: theme.typography.fontSize['3xl'],
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.textPrimary,
      textAlign: 'center',
      marginBottom: theme.spacing[3],
      letterSpacing: theme.typography.letterSpacing.tight,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.lg,
    },
    actionSection: {
      width: '100%',
      alignItems: 'center',
      marginBottom: theme.spacing[8],
    },
    buttonContainer: {
      width: '100%',
      maxWidth: 280,
      marginBottom: theme.spacing[6],
    },
    instructions: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
      paddingHorizontal: theme.spacing[4],
    },
    featuresSection: {
      width: '100%',
      maxWidth: 320,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.xl,
      padding: theme.spacing[6],
      ...theme.shadows.base,
    },
    featuresTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semiBold,
      color: theme.colors.textPrimary,
      textAlign: 'center',
      marginBottom: theme.spacing[4],
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing[3],
    },
    featureIcon: {
      marginRight: theme.spacing[3],
    },
    featureText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentContainer}>
          {/* Hero Section */}
          <View style={styles.heroSection}>
            <View style={styles.iconContainer}>
              <Icon name="image" size={60} color={theme.colors.textInverse} />
            </View>
            <Text style={styles.title}>SVG Viewer</Text>
            <Text style={styles.subtitle}>View and explore SVG files with ease</Text>
          </View>
          <AdComponent
            showAdMedia={true}
            nativeId={AdService.getNativeAdUnitId()}
            bannerId={AdService.getBannerAdUnitId()}
          />

          {/* Action Section */}
          <View style={styles.actionSection}>
            <View style={styles.buttonContainer}>
              {isPickerActive ? (
                <View style={{alignItems: 'center', paddingVertical: theme.spacing[4]}}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                  <Text style={[styles.instructions, {marginTop: theme.spacing[2]}]}>
                    Opening file picker...
                  </Text>
                </View>
              ) : (
                <Button title="Choose SVG File" onPress={handlePickDocument} size="lg" />
              )}
            </View>

            <Text style={styles.instructions}>
              Select an SVG or SVGZ file from your device to start viewing
            </Text>
          </View>
          {/* Features Section */}
          <View style={styles.featuresSection}>
            <Text style={styles.featuresTitle}>Features</Text>

            <View style={styles.featureItem}>
              <Icon
                name="zoom-in"
                size={20}
                color={theme.colors.primary}
                style={styles.featureIcon}
              />
              <Text style={styles.featureText}>Zoom and pan gestures</Text>
            </View>

            <View style={styles.featureItem}>
              <Icon
                name="layers"
                size={20}
                color={theme.colors.primary}
                style={styles.featureIcon}
              />
              <Text style={styles.featureText}>Layer management</Text>
            </View>

            <View style={styles.featureItem}>
              <Icon
                name="photo"
                size={20}
                color={theme.colors.primary}
                style={styles.featureIcon}
              />
              <Text style={styles.featureText}>Export to PNG</Text>
            </View>

            <View style={styles.featureItem}>
              <Icon
                name="fullscreen"
                size={20}
                color={theme.colors.primary}
                style={styles.featureIcon}
              />
              <Text style={styles.featureText}>Fullscreen viewing</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Banner ad at the bottom of the screen */}
      <BannerAd nativeId={AdService.getNativeAdUnitId()} bannerId={AdService.getBannerAdUnitId()} />
    </View>
  );
};

export default HomeScreen;
