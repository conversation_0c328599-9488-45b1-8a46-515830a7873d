import { Platform } from 'react-native';
import * as RNIap from 'react-native-iap';

// Product IDs for different tiers
export const productIds = {
  silver: Platform.select({
    android: 'com.svgviewerapp.silver',
    ios: 'com.svgviewerapp.silver',
  }),
  gold: Platform.select({
    android: 'com.svgviewerapp.gold',
    ios: 'com.svgviewerapp.gold',
  }),
};

// Type for purchase completion callback
export type PurchaseCompletionCallback = (purchase: RNIap.Purchase) => void;

class IapService {
  private static instance: IapService;
  private isInitialized = false;
  private purchaseUpdateSubscription: any = null;
  private purchaseErrorSubscription: any = null;
  private onPurchaseComplete: PurchaseCompletionCallback | null = null;

  private constructor() {}

  static getInstance(): IapService {
    if (!IapService.instance) {
      IapService.instance = new IapService();
    }
    return IapService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      await RNIap.initConnection();
      this.setupPurchaseListeners();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize IAP:', error);
      throw error;
    }
  }

  private setupPurchaseListeners(): void {
    // Listen for purchase updates
    this.purchaseUpdateSubscription = RNIap.purchaseUpdatedListener(
      async (purchase: RNIap.Purchase) => {
        console.log('Purchase successful:', purchase);
        
        try {
          // Finish the transaction
          await this.finishTransaction(purchase);
          
          // Call the completion callback if set
          if (this.onPurchaseComplete) {
            this.onPurchaseComplete(purchase);
          }
        } catch (error) {
          console.error('Error finishing transaction:', error);
        }
      }
    );

    // Listen for purchase errors
    this.purchaseErrorSubscription = RNIap.purchaseErrorListener(
      (error: RNIap.PurchaseError) => {
        console.error('Purchase error:', error);
      }
    );
  }

  setPurchaseCompletionCallback(callback: PurchaseCompletionCallback | null): void {
    this.onPurchaseComplete = callback;
  }

  async getProducts(): Promise<RNIap.Product[]> {
    try {
      const products = await RNIap.getProducts({ skus: Object.values(productIds).filter(Boolean) as string[] });
      return products;
    } catch (error) {
      console.error('Failed to get products:', error);
      throw error;
    }
  }

  async requestPurchase(productId: string): Promise<void> {
    try {
      await RNIap.requestPurchase({ skus: [productId] });
      // Note: The actual purchase completion is handled by the purchaseUpdatedListener
    } catch (error) {
      console.error('Failed to purchase:', error);
      throw error;
    }
  }

  async restorePurchases(): Promise<RNIap.Purchase[]> {
    try {
      const purchases = await RNIap.getAvailablePurchases();
      return purchases;
    } catch (error) {
      console.error('Failed to restore purchases:', error);
      throw error;
    }
  }

  async finishTransaction(purchase: RNIap.Purchase): Promise<void> {
    try {
      await RNIap.finishTransaction({ purchase });
    } catch (error) {
      console.error('Failed to finish transaction:', error);
      throw error;
    }
  }

  cleanup(): void {
    if (this.purchaseUpdateSubscription) {
      this.purchaseUpdateSubscription.remove();
      this.purchaseUpdateSubscription = null;
    }
    if (this.purchaseErrorSubscription) {
      this.purchaseErrorSubscription.remove();
      this.purchaseErrorSubscription = null;
    }
    this.onPurchaseComplete = null;
  }
}

export default IapService.getInstance();