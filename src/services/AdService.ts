import {Platform} from 'react-native';
import {
  InterstitialAd,
  RewardedAd,
  TestIds,
  MobileAds,
  AdEventType,
  RewardedAdEventType,
} from 'react-native-google-mobile-ads';
import {store} from '@/store';

// Mock implementation for demonstration purposes
class AdServiceImpl {
  // Using test ad unit IDs for development
  // Replace these with your actual ad unit IDs in production
  private adUnitIds = {
    banner: __DEV__
      ? TestIds.BANNER
      : Platform.select({
          ios: 'ca-app-pub-XXXXXXXXXXXXXXXX/NNNNNNNNNN', // Your iOS banner ad unit ID
          android: 'ca-app-pub-2044352253676532/4408338626', // Your Android banner ad unit ID
          default: '',
        }),
    native: __DEV__
      ? TestIds.BANNER
      : Platform.select({
          ios: 'ca-app-pub-XXXXXXXXXXXXXXXX/NNNNNNNNNN', // Your iOS banner ad unit ID
          android: 'ca-app-pub-2044352253676532/7967221949', // Your Android banner ad unit ID
          default: '',
        }),
    interstitial: __DEV__
      ? TestIds.INTERSTITIAL
      : Platform.select({
          ios: 'ca-app-pub-XXXXXXXXXXXXXXXX/NNNNNNNNNN', // Your iOS interstitial ad unit ID
          android: 'ca-app-pub-2044352253676532/4197178538', // Your Android interstitial ad unit ID
          default: '',
        }),
    rewarded: __DEV__
      ? TestIds.REWARDED
      : Platform.select({
          ios: 'ca-app-pub-XXXXXXXXXXXXXXXX/NNNNNNNNNN', // Your iOS rewarded ad unit ID
          android: 'ca-app-pub-2044352253676532/4632292801', // Your Android rewarded ad unit ID
          default: '',
        }),
  };

  private interstitialShownCount = 0;
  private readonly INTERSTITIAL_SHOW_INTERVAL = 3; // Show interstitial every 3 SVG views
  private interstitialAd: InterstitialAd | null = null;
  private rewardedAd: RewardedAd | null = null;

  constructor() {
    // Initialize ad loading
    this.initialize();
  }

  /**
   * Initialize the ad service
   * Note: MobileAds SDK is now initialized in App.tsx
   */
  private initialize() {
    // Preload interstitial ad
    this.loadInterstitialAd();

    this.loadRewardedAd();

    console.log('AdService initialized and ads preloaded');
  }

  /**
   * Load interstitial ad
   */
  private loadInterstitialAd() {
    this.interstitialAd = InterstitialAd.createForAdRequest(this.getInterstitialAdUnitId());

    // Add event listeners
    this.interstitialAd.addAdEventListener(AdEventType.LOADED, () => {
      console.log('Interstitial ad loaded');
    });

    this.interstitialAd.addAdEventListener(AdEventType.ERROR, error => {
      console.error('Interstitial ad error:', error);
      // Retry loading after error
      setTimeout(() => this.loadInterstitialAd(), 5000);
    });

    this.interstitialAd.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('Interstitial ad closed');
      // Reload ad after it's been closed
      this.loadInterstitialAd();
    });

    // Load the ad
    this.interstitialAd.load();
  }

  /**
   * Load rewarded ad
   */
  private loadRewardedAd() {
    this.rewardedAd = RewardedAd.createForAdRequest(this.getRewardedAdUnitId());

    // Add event listeners
    this.rewardedAd.addAdEventListener(RewardedAdEventType.LOADED, () => {
      console.log('Rewarded ad loaded');
    });

    this.rewardedAd.addAdEventListener(AdEventType.ERROR, error => {
      console.error('Rewarded ad error:', error);
      // Retry loading after error
      setTimeout(() => this.loadRewardedAd(), 5000);
    });

    // Load the ad
    this.rewardedAd.load();
  }

  /**
   * Get banner ad unit ID
   */
  getBannerAdUnitId() {
    return this.adUnitIds.banner;
  }
  /**
   * Get banner ad unit ID
   */
  getNativeAdUnitId() {
    return this.adUnitIds.native;
  }

  /**
   * Get interstitial ad unit ID
   */
  getInterstitialAdUnitId() {
    return this.adUnitIds.interstitial;
  }

  /**
   * Get rewarded ad unit ID
   */
  getRewardedAdUnitId() {
    return this.adUnitIds.rewarded;
  }

  /**
   * Check if ads should be shown based on subscription status
   * @returns boolean - Whether ads should be shown
   */
  shouldShowAds(): boolean {
    const state = store.getState();
    const {hasSilver, hasGold} = state.iap;

    // Don't show ads if user has silver or gold package
    return !(hasSilver || hasGold);
  }

  /**
   * Show interstitial ad based on view count
   * @returns Promise<boolean> - Whether the ad was shown
   */
  async showInterstitialIfNeeded(): Promise<boolean> {
    // Don't show ads if user has purchased a package
    if (!this.shouldShowAds()) {
      return false;
    }

    this.interstitialShownCount++;

    if (this.interstitialShownCount % this.INTERSTITIAL_SHOW_INTERVAL === 0) {
      return await this.showInterstitial();
    }

    return false;
  }

  /**
   * Show interstitial ad
   * @returns Promise<boolean> - Whether the ad was shown
   */
  async showInterstitial(): Promise<boolean> {
    // Don't show ads if user has purchased a package
    if (!this.shouldShowAds()) {
      return false;
    }

    if (!this.interstitialAd) {
      console.log('Interstitial ad not loaded yet');
      this.loadInterstitialAd();
      return false;
    }

    if (!this.interstitialAd.loaded) {
      console.log('Interstitial ad still loading');
      return false;
    }

    try {
      console.log('Showing interstitial ad');
      await this.interstitialAd.show();
      return true;
    } catch (error) {
      console.error('Error showing interstitial ad:', error);
      // Reload the ad after error
      this.loadInterstitialAd();
      return false;
    }
  }

  /**
   * Show rewarded ad
   * @returns Promise<boolean> - Whether the ad was shown and reward earned
   */
  async showRewardedAd(callback: () => void): Promise<boolean> {
    if (!this.shouldShowAds()) {
      return false;
    }

    if (!this.rewardedAd) {
      console.log('Rewarded ad not loaded yet');
      this.loadRewardedAd();
      return false;
    }

    if (!this.rewardedAd.loaded) {
      console.log('Rewarded ad still loading');
      return false;
    }

    this.rewardedAd.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('Rewarded ad closed');
      // Reload ad after it's been closed
      callback();
      this.loadRewardedAd();
    });
    try {
      console.log('Showing rewarded ad');
      await this.rewardedAd.show();
      return true;
    } catch (error) {
      console.error('Error showing rewarded ad:', error);
      // Reload the ad after error
      this.loadRewardedAd();
      return false;
    }
  }
}

// Export as singleton
const AdService = new AdServiceImpl();
export default AdService;
