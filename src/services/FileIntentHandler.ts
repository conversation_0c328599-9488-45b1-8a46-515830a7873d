import { useEffect } from 'react';
import { Platform } from 'react-native';
import ReceiveSharingIntent from 'react-native-receive-sharing-intent';
import { Dirs,FileSystem } from 'react-native-file-access';
import { useNavigation } from '@react-navigation/native';

// Define the type for the navigation prop
type NavigationProp = {
  navigate: (screen: string, params?: any) => void;
};

// Define the type for the function that adds a file to recent files
type AddToRecentFilesFunction = (filePath: string, fileName: string) => void;

/**
 * Hook to handle files shared to the app via intents
 * @param addToRecentFiles Function to add the file to recent files list
 */
export const useFileIntentHandler = (addToRecentFiles: AddToRecentFilesFunction) => {
  const navigation = useNavigation<NavigationProp>();

  useEffect(() => {
    // Only run on Android platform
    if (Platform.OS !== 'android') return;

    // Function to process the received file
    const processReceivedFile = async (filePath: string) => {
      try {
        // Extract file name from path for recent files
        const fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
        
        // Add to recent files if callback is provided
        if (addToRecentFiles) {
          addToRecentFiles(filePath, fileName);
        }
        
        // Navigate to the SVG viewer screen with the correct parameter name (uri instead of filePath)
        navigation.navigate('SvgViewer', { uri: filePath, fileName });
      } catch (error) {
        console.error('Error processing received file:', error);
      }
    };

    // Add a delay to ensure the app is fully initialized before checking for sharing intents
    const timeoutId = setTimeout(() => {
      // Set up the sharing intent listener with better error handling
      try {
        ReceiveSharingIntent.getReceivedFiles(
          (files: string | any[]) => {
            // Handle received files
            if (files && files.length > 0) {
              const file = files[0];
              if (file && file.filePath) {
                processReceivedFile(file.filePath);
              }
            }
          },
          (error: { message: any; stack: any; }) => {
            // Only log actual errors, not null intent cases
            if (error && error.message && !error.message.includes('NullPointerException')) {
              console.error('Error receiving files:', error);
              // Log more detailed error information to help with debugging
              if (error instanceof Error) {
                console.error('Error details:', error.message, error.stack);
              }
            }
          },
          // Filter for SVG files
          'image/svg+xml'
        );
      } catch (error) {
        // Catch any synchronous errors during setup
        console.warn('Failed to set up sharing intent listener:', error);
      }
    }, 1000); // Wait 1 second for app to fully initialize

    // Clean up function
    return () => {
      clearTimeout(timeoutId);
      try {
        ReceiveSharingIntent.clearReceivedFiles();
      } catch (error) {
        // Ignore cleanup errors
      }
    };
  }, [navigation, addToRecentFiles]);
};