import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import HomeScreen from '@/screens/HomeScreen';
import SvgViewerScreen from '@/screens/SvgViewerScreen';
import SettingsScreen from '@/screens/SettingsScreen';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {TouchableOpacity, useColorScheme} from 'react-native';
import {useTheme} from '@/theme';

export type RootStackParamList = {
  Home: undefined;
  SvgViewer: {uri: string; fileName: string};
  Settings: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  const theme = useTheme();

  const screenOptions = {
    headerStyle: {
      backgroundColor: theme.colors.surface,
    },
    headerTintColor: theme.colors.textPrimary,
    headerTitleStyle: {
      fontWeight: theme.typography.fontWeight.semiBold,
      fontSize: theme.typography.fontSize.lg,
    },
    headerShadowVisible: true,
  };

  return (
    <Stack.Navigator initialRouteName="Home" screenOptions={screenOptions}>
      <Stack.Screen
        name="Home"
        component={HomeScreen}
        options={({navigation}) => ({
          title: 'SVG Viewer',
          headerRight: () => (
            <TouchableOpacity
              onPress={() => navigation.navigate('Settings')}
              style={{
                marginRight: theme.spacing[2],
                padding: theme.spacing[2],
                borderRadius: theme.borderRadius.md,
                backgroundColor: theme.colors.background,
                ...theme.shadows.sm,
              }}>
              <Icon name="settings" size={24} color={theme.colors.primary} />
            </TouchableOpacity>
          ),
        })}
      />
      <Stack.Screen
        name="SvgViewer"
        component={SvgViewerScreen}
        options={{
          title: 'SVG Viewer',
        }}
      />
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Settings',
        }}
      />
    </Stack.Navigator>
  );
};

export default AppNavigator;
