/**
 * Example usage of the optimized async SVG parser
 * This file demonstrates how to use the new async functions for better performance
 */

import {
  parseSvgLayersAsync,
  updateSvgWithLayerVisibilityAsync,
  preloadSvgContent,
  optimizeCacheMemory,
  getCacheStats,
  clearSvgCaches,
  type SvgLayer,
  type LayerDetectionConfig,
  type ProgressCallback
} from './svgParser';

/**
 * Example: Basic async layer parsing with progress reporting
 */
export const parseLayersWithProgress = async (svgContent: string): Promise<SvgLayer[]> => {
  const progressCallback: ProgressCallback = (progress, stage) => {
    console.log(`Progress: ${progress}% - ${stage}`);
  };

  try {
    const layers = await parseSvgLayersAsync(svgContent, {}, progressCallback);
    console.log(`Found ${layers.length} layers`);
    return layers;
  } catch (error) {
    console.error('Failed to parse SVG layers:', error);
    return [];
  }
};

/**
 * Example: Advanced layer parsing with custom configuration
 */
export const parseLayersAdvanced = async (svgContent: string): Promise<SvgLayer[]> => {
  const config: Partial<LayerDetectionConfig> = {
    layerElements: ['g', 'path', 'rect', 'circle'], // Only consider these elements
    minChildrenForGroup: 2, // Groups must have at least 2 children
    includeSingleElements: true, // Include individual elements as layers
    detectNestedLayers: true,
    maxDepth: 5 // Limit nesting depth for performance
  };

  const progressCallback: ProgressCallback = (progress, stage) => {
    // Update UI progress bar here
    console.log(`Parsing: ${progress}% - ${stage}`);
  };

  return await parseSvgLayersAsync(svgContent, config, progressCallback);
};

/**
 * Example: Async layer visibility update with progress
 */
export const updateLayerVisibilityWithProgress = async (
  svgContent: string,
  layers: SvgLayer[]
): Promise<string> => {
  const progressCallback: ProgressCallback = (progress, stage) => {
    console.log(`Updating visibility: ${progress}% - ${stage}`);
  };

  try {
    const updatedSvg = await updateSvgWithLayerVisibilityAsync(
      svgContent,
      layers,
      {}, // Use default config
      progressCallback
    );
    
    console.log('Successfully updated layer visibility');
    return updatedSvg;
  } catch (error) {
    console.error('Failed to update layer visibility:', error);
    return svgContent; // Return original on error
  }
};

/**
 * Example: Batch processing multiple SVG files
 */
export const processSvgBatch = async (svgFiles: { content: string; name: string }[]): Promise<void> => {
  console.log(`Processing ${svgFiles.length} SVG files...`);

  // Preload all SVG content for faster processing
  const svgContents = svgFiles.map(file => file.content);
  await preloadSvgContent(svgContents, (progress, stage) => {
    console.log(`Preloading: ${progress}% - ${stage}`);
  });

  // Process each file
  for (let i = 0; i < svgFiles.length; i++) {
    const file = svgFiles[i];
    console.log(`\nProcessing ${file.name}...`);

    try {
      // Parse layers
      const layers = await parseSvgLayersAsync(file.content, {}, (progress, stage) => {
        console.log(`  ${file.name} parsing: ${progress}% - ${stage}`);
      });

      console.log(`  Found ${layers.length} layers in ${file.name}`);

      // Example: Hide all layers except the first one
      const modifiedLayers = layers.map((layer, index) => ({
        ...layer,
        visible: index === 0
      }));

      // Update visibility
      const updatedSvg = await updateSvgWithLayerVisibilityAsync(
        file.content,
        modifiedLayers,
        {},
        (progress, stage) => {
          console.log(`  ${file.name} updating: ${progress}% - ${stage}`);
        }
      );

      console.log(`  Successfully processed ${file.name}`);
      
      // Here you would save or use the updatedSvg
      
    } catch (error) {
      console.error(`  Failed to process ${file.name}:`, error);
    }

    // Optimize memory usage periodically
    if (i % 10 === 0) {
      optimizeCacheMemory(50); // Keep max 50 entries per cache
      console.log('  Optimized cache memory');
    }
  }

  // Show final cache statistics
  const stats = getCacheStats();
  console.log('\nFinal cache statistics:', stats);
};

/**
 * Example: Memory management and monitoring
 */
export const demonstrateMemoryManagement = async (): Promise<void> => {
  console.log('=== Memory Management Demo ===');

  // Show initial cache state
  console.log('Initial cache stats:', getCacheStats());

  // Process some SVG content
  const sampleSvg = `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
      <g id="layer1">
        <rect x="10" y="10" width="30" height="30" fill="red"/>
      </g>
      <g id="layer2">
        <circle cx="70" cy="70" r="15" fill="blue"/>
      </g>
    </svg>
  `;

  // Parse multiple times to fill cache
  for (let i = 0; i < 5; i++) {
    const modifiedSvg = sampleSvg.replace('layer1', `layer1_${i}`);
    await parseSvgLayersAsync(modifiedSvg);
  }

  console.log('After processing:', getCacheStats());

  // Optimize memory
  optimizeCacheMemory(3); // Keep only 3 entries per cache
  console.log('After optimization:', getCacheStats());

  // Clear all caches
  clearSvgCaches();
  console.log('After clearing:', getCacheStats());
};

