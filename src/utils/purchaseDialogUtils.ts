import { Alert } from 'react-native';
import IapService, { productIds } from '@/services/IapService';
import AdService from '@/services/AdService';
import { store } from '@/store';
import { processPurchaseCompletion } from '@/store/slices/iapSlice';
import remoteConfig from '@react-native-firebase/remote-config';

export async function showPurchaseDialog(message: string, savePngCallback: () => void,showSilver:boolean=true) {
  // Set up purchase completion callback

  const showRewardedAd = remoteConfig().getBoolean('ShowAd');
  console.log('Show Ad:', showRewardedAd);
  IapService.setPurchaseCompletionCallback((purchase) => {
    console.log('Purchase completed:', purchase.productId);
    
    // Dispatch the purchase completion to update Redux state
    store.dispatch(processPurchaseCompletion(purchase));
    
    // Show success message
    Alert.alert(
      'Purchase Successful!',
      'Your premium features have been activated.',
      [{ text: 'OK' }]
    );
    
    // Clear the callback after use
    IapService.setPurchaseCompletionCallback(null);
  });

  // Helper function for handling ad watching
  const handleWatchAd = async () => {
    const adShown = await AdService.showRewardedAd(savePngCallback);
    if (adShown) {
      console.log('Ad watched successfully');
    } else {
      Alert.alert('Error', 'Failed to load advertisement. Please try again.');
    }
  };

  // Helper function for handling purchases
  const handlePurchase = async (productId: string | undefined) => {
    try {
      if (!productId) {
        throw new Error('Product ID not found for this platform');
      }
      await IapService.requestPurchase(productId);
      // Note: Success handling is done via the purchase completion callback
    } catch (error) {
      console.error('Purchase error:', error);
      Alert.alert('Error', 'Failed to process purchase. Please try again.');
      // Clear the callback on error
      IapService.setPurchaseCompletionCallback(null);
    }
  };

  // First fetch the products to get the actual prices
  try {
    const products = await IapService.getProducts();

    // Find the silver and gold products
    const silverProduct = products.find(p => p.productId === productIds.silver);
    const goldProduct = products.find(p => p.productId === productIds.gold);

    const buttons: Array<{
      text: string;
      style?: 'cancel' | 'default' | 'destructive';
      onPress?: () => Promise<void>;
    }> = [];

    // Conditionally add 'Watch Ad' button
    if (showRewardedAd) {
      buttons.push({
        text: 'Watch Ad',
        onPress: handleWatchAd
      });
    }
    
    // Add other buttons
    buttons.push({
      text: `Buy Gold (${goldProduct?.localizedPrice || 'Loading...'})`,
      onPress: () => handlePurchase(productIds.gold)
    });

    if(showSilver){
      buttons.push({
        text: `Buy Silver (${silverProduct?.localizedPrice || 'Loading...'})`,
        onPress: () => handlePurchase(productIds.silver)
      });
    }

    Alert.alert('Premium Feature', message, buttons, {
      cancelable: true
    });
  } catch (error) {
    console.error('Error fetching product prices:', error);

    // Fallback to showing dialog without prices if fetching fails
    const buttons: Array<{
      text: string;
      style?: 'cancel' | 'default' | 'destructive';
      onPress?: () => Promise<void>;
    }> = [];

    // Conditionally add 'Watch Ad' button in fallback
    if (showRewardedAd) {
      buttons.push({
        text: 'Watch Ad',
        onPress: handleWatchAd
      });
    }

    // Add other buttons in fallback
    buttons.push({
      text: 'Buy Gold',
      onPress: () => handlePurchase(productIds.gold)
    });

    if(showSilver){
      buttons.push( {
        text: 'Buy Silver',
        onPress: () => handlePurchase(productIds.silver)
      });
    }
    Alert.alert('Premium Feature', message, buttons, {
      cancelable: true
    });
  }
}