/**
 * Performance testing utilities for SVG parser functions
 */

import { parseSvgLayers, parseSvgLayersOptimized, parseSvgLayersAsync } from './svgParser';

/**
 * Creates a test SVG with specified number of groups
 */
const createTestSvg = (groupCount: number): string => {
  const groups = Array.from({ length: groupCount }, (_, i) => 
    `<g id="layer${i}" inkscape:label="Layer ${i}">
      <rect x="${i * 10}" y="${i * 10}" width="50" height="50" fill="red"/>
    </g>`
  ).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" 
     viewBox="0 0 1000 1000">
  ${groups}
</svg>`;
};

/**
 * Performance test for synchronous functions
 */
export const testSyncPerformance = (groupCount: number = 1000) => {
  const testSvg = createTestSvg(groupCount);
  
  console.log(`\n=== Performance Test (${groupCount} groups) ===`);
  
  // Test original function
  const start1 = performance.now();
  const layers1 = parseSvgLayers(testSvg);
  const end1 = performance.now();
  
  // Test optimized function
  const start2 = performance.now();
  const layers2 = parseSvgLayersOptimized(testSvg);
  const end2 = performance.now();
  
  console.log(`Original function: ${(end1 - start1).toFixed(2)}ms (${layers1.length} layers)`);
  console.log(`Optimized function: ${(end2 - start2).toFixed(2)}ms (${layers2.length} layers)`);
  console.log(`Performance improvement: ${((end1 - start1) / (end2 - start2)).toFixed(2)}x faster`);
  
  return {
    original: { time: end1 - start1, layers: layers1.length },
    optimized: { time: end2 - start2, layers: layers2.length }
  };
};

/**
 * Performance test for async function
 */
export const testAsyncPerformance = async (groupCount: number = 1000) => {
  const testSvg = createTestSvg(groupCount);
  
  console.log(`\n=== Async Performance Test (${groupCount} groups) ===`);
  
  // Test async function
  const start = performance.now();
  const layers = await parseSvgLayersAsync(testSvg);
  const end = performance.now();
  
  console.log(`Async function: ${(end - start).toFixed(2)}ms (${layers.length} layers)`);
  
  return {
    time: end - start,
    layers: layers.length
  };
};

/**
 * Comprehensive performance test
 */
export const runPerformanceTests = async () => {
  console.log('🚀 Starting SVG Parser Performance Tests...');
  
  // Test with different group counts
  const testSizes = [100, 500, 1000, 2000];
  
  for (const size of testSizes) {
    testSyncPerformance(size);
    await testAsyncPerformance(size);
  }
  
  console.log('\n✅ Performance tests completed!');
};

/**
 * Memory usage test
 */
export const testMemoryUsage = () => {
  const testSvg = createTestSvg(1000);
  
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
  
  const memBefore = process.memoryUsage();
  
  // Parse multiple times to test caching
  for (let i = 0; i < 10; i++) {
    parseSvgLayersOptimized(testSvg);
  }
  
  const memAfter = process.memoryUsage();
  
  console.log('\n=== Memory Usage Test ===');
  console.log(`Heap used before: ${(memBefore.heapUsed / 1024 / 1024).toFixed(2)} MB`);
  console.log(`Heap used after: ${(memAfter.heapUsed / 1024 / 1024).toFixed(2)} MB`);
  console.log(`Memory increase: ${((memAfter.heapUsed - memBefore.heapUsed) / 1024 / 1024).toFixed(2)} MB`);
};
