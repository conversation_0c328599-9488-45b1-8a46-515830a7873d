import { DOM<PERSON>arser} from '@xmldom/xmldom';
/**
 * Interface representing an SVG layer
 */
export interface SvgLayer {
  id: string;
  name: string;
  visible: boolean;
}

// Cache for parsed SVG documents to avoid repeated parsing
const svgDocCache = new Map<string, Document>();

// Cache for extracted layers to avoid repeated layer extraction
const layersCache = new Map<string, SvgLayer[]>();

/**
 * Parses an SVG string and extracts layer information
 * @param svgContent The SVG content as a string
 * @returns Array of identified layers
 */
export const parseSvgLayers = (svgContent: string): SvgLayer[] => {
  try {
    // Check cache first
    const cacheKey = hashString(svgContent);
    if (layersCache.has(cacheKey)) {
      return layersCache.get(cacheKey) || [];
    }
    
    // Get or parse the SVG document
    const svgDoc = getSvgDoc(svgContent);
    
    // Find all group elements that might represent layers
    const layers: SvgLayer[] = [];
    
    const groups = (svgDoc as Document).getElementsByTagName('g');
    
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      let id = group.getAttribute('id');
      
      // Skip groups without IDs
      if (!id) id = String(i + 1);
      
      // Try to get a more user-friendly name
      // First check for inkscape:label which is commonly used in Inkscape
      let name = group.getAttribute('inkscape:label');
      
      // If no inkscape label, check for a title element child
      if (!name) {
        const titleElements = group.getElementsByTagName('title');
        if (titleElements.length > 0) {
          name = titleElements[0].textContent || '';
        }
      }
      
      // If still no name, use the ID
      if (!name) {
        name = id;
      }
      
      // Add to layers array
      layers.push({
        id,
        name,
        visible: true // Default to visible
      });
    }
    
    // Cache the result
    layersCache.set(cacheKey, layers);
    
    return layers;
  } catch (error) {
    console.error('Error parsing SVG layers:', error);
    return [];
  }
};

/**
 * Get SVG document from cache or parse it
 * @param svgContent SVG content as string
 * @returns Parsed SVG document
 */
const getSvgDoc = (svgContent: string): Document => {
  const cacheKey = hashString(svgContent);
  
  if (svgDocCache.has(cacheKey)) {
    return svgDocCache.get(cacheKey)!;
  }
  
  // Parse the SVG XML
  const parser = new DOMParser();
  const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml') as Document;
  
  // Cache the parsed document
  svgDocCache.set(cacheKey, svgDoc);
  
  return svgDoc;
};

/**
 * Simple string hashing function for cache keys
 * @param str String to hash
 * @returns Hash value as string
 */
const hashString = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString();
};

/**
 * Updates SVG content based on layer visibility
 * @param svgContent Original SVG content
 * @param layers Array of layers with visibility information
 * @returns Updated SVG content with visibility applied
 */
export const updateSvgWithLayerVisibility = (svgContent: string, layers: SvgLayer[]): string => {
  try {
    // Create a map of layer IDs to visibility for quick lookup
    const visibilityMap = new Map<string, boolean>();
    
    // Check if any layer visibility has changed
    let hasVisibilityChanged = false;
    const cacheKey = hashString(svgContent);
    const cachedVisibilityKey = `${cacheKey}_visibility`;
    const previousVisibilityState = svgVisibilityCache.get(cachedVisibilityKey);
    
    // Build current visibility state
    const currentVisibilityState: Record<string, boolean> = {};
    
    layers.forEach(layer => {
      visibilityMap.set(layer.id, layer.visible);
      currentVisibilityState[layer.id] = layer.visible;
      
      // Check if this layer's visibility changed from previous state
      if (previousVisibilityState && 
          previousVisibilityState[layer.id] !== undefined && 
          previousVisibilityState[layer.id] !== layer.visible) {
        hasVisibilityChanged = true;
      }
    });
    
    // If visibility hasn't changed and we have a cached result, return it
    if (!hasVisibilityChanged && svgResultCache.has(cachedVisibilityKey)) {
      return svgResultCache.get(cachedVisibilityKey) || svgContent;
    }
    
    // Get the SVG document from cache or parse it
    const svgDoc = getSvgDoc(svgContent);
    
    // Get all <g> elements
    const groups = svgDoc.getElementsByTagName('g');
    
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      let id = group.getAttribute('id');
      
      // Skip groups without IDs
      if (!id) id=String(i+1);
      
      // Check if this group is in our layer map
      if (visibilityMap.has(id)) {
        const isVisible = visibilityMap.get(id);
        
        // Set display attribute based on visibility
        if (isVisible) {
          // Remove display="none" if it exists
          if (group.getAttribute('display') === 'none') {
            group.removeAttribute('display');
          }
        } else {
          // Add display="none" to hide the layer
          group.setAttribute('display', 'none');
        }
      }
    }
    
    // Serialize the modified document back to a string
    const result = svgDoc.toString();
    
    // Cache the result and visibility state
    svgResultCache.set(cachedVisibilityKey, result);
    svgVisibilityCache.set(cachedVisibilityKey, {...currentVisibilityState});
    
    return result;
  } catch (error) {
    console.error('Error updating SVG layer visibility:', error);
    return svgContent; // Return original content on error
  }
};

// Cache for visibility states
const svgVisibilityCache = new Map<string, Record<string, boolean>>();

// Cache for rendered SVG results
const svgResultCache = new Map<string, string>();

/**
 * Clears all SVG parsing caches
 * Call this when memory usage is a concern or when SVGs are no longer needed
 */
export const clearSvgCaches = (): void => {
  svgDocCache.clear();
  layersCache.clear();
  svgVisibilityCache.clear();
  svgResultCache.clear();
};