import { FileSystem } from 'react-native-file-access';
import pako from 'pako';

/**
 * Checks if a file is an SVGZ file based on its extension
 * @param filePath Path to the file
 * @returns boolean indicating if the file is an SVGZ file
 */
export const isSvgzFile = (filePath: string): boolean => {
  return filePath.toLowerCase().endsWith('.svgz');
};

/**
 * Checks if a file is an SVG file based on its extension
 * @param filePath Path to the file
 * @returns boolean indicating if the file is an SVG file
 */
export const isSvgFile = (filePath: string): boolean => {
  return filePath.toLowerCase().endsWith('.svg');
};

/**
 * Detects if the content appears to be gzipped based on magic bytes
 * @param buffer The binary buffer to check
 * @returns boolean indicating if the content appears to be gzipped
 */
export const isGzipped = (buffer: Buffer): boolean => {
  // Check for gzip magic bytes (1F 8B)
  return buffer.length >= 2 && buffer[0] === 0x1F && buffer[1] === 0x8B;
};

/**
 * Reads an SVG file and returns its content as a string
 * @param filePath Path to the SVG file
 * @returns Promise resolving to the SVG content as a string
 */
export const readSvgFile = async (filePath: string): Promise<string> => {
  return await FileSystem.readFile(filePath, 'utf8');
};

/**
 * Reads an SVGZ file, decompresses it, and returns its content as a string
 * @param filePath Path to the SVGZ file
 * @returns Promise resolving to the decompressed SVG content as a string
 */
export const readSvgzFile = async (filePath: string): Promise<string> => {
  // Read the file as a base64 encoded string
  const base64Data = await FileSystem.readFile(filePath, 'base64');

  // Convert base64 to binary data
  const binaryData = Buffer.from(base64Data, 'base64');
  
  // Verify this is actually a gzipped file
  if (!isGzipped(binaryData)) {
    throw new Error('File is not in gzip format despite .svgz extension');
  }
  
  // Decompress the gzipped content using pako
  try {
    const decompressedData = pako.inflate(binaryData);
    
    // Convert the decompressed Uint8Array to a string
    const decoder = new TextDecoder('utf-8');
    const svgString = decoder.decode(decompressedData);
    
    return svgString;
  } catch (error) {
    throw new Error(`Failed to decompress SVGZ file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Reads and returns the content of an SVG or SVGZ file
 * @param filePath Path to the SVG or SVGZ file
 * @returns Promise resolving to the SVG content as a string
 */
export const readSvgContent = async (filePath: string): Promise<string> => {
  try {
    if (isSvgzFile(filePath)) {
      return await readSvgzFile(filePath);
    } else {
      // For regular SVG files
      return await readSvgFile(filePath);
    }
  } catch (error) {
    throw new Error(`Error reading SVG/SVGZ file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Validates if the content is a valid SVG
 * @param content SVG content to validate
 * @returns boolean indicating if the content is valid SVG
 */
export const isValidSvgContent = (content: string): boolean => {
  return content.includes('<svg') && content.includes('</svg>');
}; 