import { Alert, Platform, Dimensions } from 'react-native';
import { captureRef } from 'react-native-view-shot';
import { FileSystem } from 'react-native-file-access';
import ViewShot from 'react-native-view-shot';

export async function savePng(
  viewShotRef: React.RefObject<ViewShot | null>,
  fileName: string
) {
  if (!viewShotRef.current) return;

  const windowWidth = Dimensions.get('window').width;
  const windowHeight = Dimensions.get('window').height;

  try {
    const screenshotUri = await captureRef(viewShotRef, {
      format: 'png',
      quality: 1,
      result: 'tmpfile',
      height: (windowHeight * 0.6) * 2,
      width: (windowWidth * 0.9) * 2
    });

    const timestamp = new Date().getTime();
    const baseFileName = fileName.replace(/\.svg(z)?$/i, '');
    const pngFileName = `${baseFileName}_${timestamp}.png`;

    await FileSystem.cpExternal(screenshotUri, pngFileName, 'images');

    Alert.alert(
      'Success',
      Platform.select({
        android: 'PNG saved to your pictures folder',
        ios: 'PNG saved to app documents folder'
      }),
      [{text: 'OK'}]
    );

    return true;
  } catch (error) {
    console.error('PNG conversion error:', error);
    Alert.alert(
      'Error',
      'Failed to convert to PNG. Please try again.',
      [{text: 'OK'}]
    );

    return false;
  }
}
