import { useState, useEffect } from 'react';
import { readSvgContent, isValidSvgContent, isSvgzFile } from '@/utils/svgHelper';
import { parseSvgLayersAsync, clearSvgCaches, updateSvgWithLayerVisibility, SvgLayer } from '@/utils/svgParser';
import { FileSystem } from 'react-native-file-access';
import AdService from '@/services/AdService';

export function useSvgLoader(uri: string) {
  const [svgContent, setSvgContent] = useState<string | null>(null);
  const [originalSvgContent, setOriginalSvgContent] = useState<string | null>(null);
  const [fileInfo, setFileInfo] = useState<{size: string; lastModified: string; isCompressed: boolean} | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [layers, setLayers] = useState<SvgLayer[]>([]);
  const [fileName, setFileName] = useState('');

  useEffect(() => {
    const loadSvg = async () => {
      setLoading(true);
      setError(null);
      
      // Clear caches when loading a new SVG
      clearSvgCaches();
      
      // Show interstitial ad if needed
      await AdService.showInterstitialIfNeeded();

      try {
        const isCompressed = isSvgzFile(uri);
        
        // Extract file name from URI
        const fileNameMatch = uri.match(/([^/]+)$/);
        const extractedFileName = fileNameMatch ? fileNameMatch[1] : 'Unknown';
        setFileName(extractedFileName);

        const fileStats = await FileSystem.stat(uri);
        console.log('File stats:', fileStats);

        const fileSizeInBytes = fileStats.size;
        let fileSize: string;

        if (fileSizeInBytes < 1024) {
          fileSize = `${fileSizeInBytes} B`;
        } else if (fileSizeInBytes < 1024 * 1024) {
          fileSize = `${(fileSizeInBytes / 1024).toFixed(1)} KB`;
        } else {
          fileSize = `${(fileSizeInBytes / (1024 * 1024)).toFixed(1)} MB`;
        }

        const lastModified = new Date(fileStats.lastModified).toLocaleString();

        setFileInfo({size: fileSize, lastModified, isCompressed});

        const content = await readSvgContent(uri);

        if (!isValidSvgContent(content)) {
          throw new Error('Invalid SVG file format');
        }

        setOriginalSvgContent(content);

        const svgLayers = await parseSvgLayersAsync(content);
        setLayers(svgLayers);

        setSvgContent(content);
      } catch (e) {
        console.error('Error loading SVG:', e);
        setError(`Failed to load SVG file: ${e instanceof Error ? e.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    loadSvg();
    
    // Cleanup function to clear caches when component unmounts
    return () => {
      clearSvgCaches();
    };
  }, [uri]);

  // Update SVG content when layers visibility changes
  useEffect(() => {
    if (layers.length > 0 && originalSvgContent) {
      const updatedSvg = updateSvgWithLayerVisibility(originalSvgContent, layers);
      setSvgContent(updatedSvg);
    }
  }, [layers, originalSvgContent]);

  const toggleLayerVisibility = (layerId: string, visible: boolean) => {
    setLayers(prevLayers => {
      const updatedLayers = prevLayers.map(layer => {
        if (layer.id === layerId) {
          return { ...layer, visible };
        }
        return layer;
      });
      
      return updatedLayers;
    });
  };

  return {
    svgContent,
    originalSvgContent,
    fileInfo,
    loading,
    error,
    layers,
    fileName,
    toggleLayerVisibility
  };
}
