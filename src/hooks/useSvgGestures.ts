import { useCallback, useMemo } from 'react';
import { Gesture } from 'react-native-gesture-handler';
import { useSharedValue, withSpring, runOnJS, useAnimatedStyle } from 'react-native-reanimated';

export function useSvgGestures(toggleControls: () => void) {
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotation = useSharedValue(0);
  const savedScale = useSharedValue(1);
  const savedTranslateX = useSharedValue(0);
  const savedTranslateY = useSharedValue(0);
  const savedRotation = useSharedValue(0);
  const lastTapTimestamp = useSharedValue(0);

  const resetView = useCallback(() => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    rotation.value = withSpring(0);
    savedScale.value = 1;
    savedTranslateX.value = 0;
    savedTranslateY.value = 0;
    savedRotation.value = 0;
  }, [scale, translateX, translateY, rotation, savedScale, savedTranslateX, savedTranslateY, savedRotation]);

  const zoomIn = useCallback(() => {
    const newScale = Math.min(savedScale.value + 0.5, 5);
    scale.value = withSpring(newScale);
    savedScale.value = newScale;
  }, [scale, savedScale]);

  const zoomOut = useCallback(() => {
    const newScale = Math.max(savedScale.value - 0.5, 0.5);
    scale.value = withSpring(newScale);
    savedScale.value = newScale;
  }, [scale, savedScale]);

  const rotateLeft = useCallback(() => {
    const newRotation = savedRotation.value - 90;
    rotation.value = withSpring(newRotation);
    savedRotation.value = newRotation;
  }, [rotation, savedRotation]);

  const rotateRight = useCallback(() => {
    const newRotation = savedRotation.value + 90;
    rotation.value = withSpring(newRotation);
    savedRotation.value = newRotation;
  }, [rotation, savedRotation]);

  // Pinch gesture for zooming
  const pinchGesture = useMemo(() => {
    return Gesture.Pinch()
      .onUpdate((e) => {
        scale.value = savedScale.value * e.scale;
      })
      .onEnd(() => {
        savedScale.value = scale.value;
      });
  }, [scale, savedScale]);

  // Rotation gesture
  const rotationGesture = useMemo(() => {
    return Gesture.Rotation()
      .onUpdate((e) => {
        rotation.value = savedRotation.value + (e.rotation * 180) / Math.PI;
      })
      .onEnd(() => {
        savedRotation.value = rotation.value;
      });
  }, [rotation, savedRotation]);

  // Pan gesture for moving
  const panGesture = useMemo(() => {
    return Gesture.Pan()
      .minPointers(1)
      .maxPointers(2)
      .onUpdate((e) => {
        translateX.value = savedTranslateX.value + e.translationX;
        translateY.value = savedTranslateY.value + e.translationY;
      })
      .onEnd(() => {
        savedTranslateX.value = translateX.value;
        savedTranslateY.value = translateY.value;
      });
  }, [translateX, translateY, savedTranslateX, savedTranslateY]);

  // Double tap gesture for zooming in/out
  const doubleTapGesture = useMemo(() => {
    return Gesture.Tap()
      .numberOfTaps(2)
      .maxDuration(250)
      .onStart(() => {
        const now = Date.now();
        if (now - lastTapTimestamp.value < 500) {
          if (scale.value > 1) {
            runOnJS(resetView)();
          } else {
            scale.value = withSpring(2);
            savedScale.value = 2;
          }
        }
        lastTapTimestamp.value = now;
        runOnJS(toggleControls)();
      });
  }, [scale, savedScale, lastTapTimestamp, resetView, toggleControls]);

  // Single tap gesture for toggling controls
  const singleTapGesture = useMemo(() => {
    return Gesture.Tap()
      .maxDuration(250)
      .onStart(() => {
        lastTapTimestamp.value = Date.now();
        runOnJS(toggleControls)();
      });
  }, [lastTapTimestamp, toggleControls]);

  // Compose gestures
  const composedGesture = useMemo(() => {
    return Gesture.Simultaneous(
      Gesture.Exclusive(doubleTapGesture, singleTapGesture),
      Gesture.Simultaneous(pinchGesture, panGesture, rotationGesture)
    );
  }, [pinchGesture, panGesture, rotationGesture, doubleTapGesture, singleTapGesture]);

  // Animated style for the SVG container
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
        { rotate: `${rotation.value}deg` }
      ]
    };
  });

  return {
    scale,
    translateX,
    translateY,
    rotation,
    savedScale,
    savedTranslateX,
    savedTranslateY,
    savedRotation,
    resetView,
    zoomIn,
    zoomOut,
    rotateLeft,
    rotateRight,
    composedGesture,
    animatedStyle
  };
}
