package com.svgviewerapp;

import android.app.Activity;
import android.content.Intent;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;

/**
 * Fixed version of ReceiveSharingIntentModule that properly handles null intents
 * to prevent NullPointerException when accessing intent.getAction()
 */
public class FixedReceiveSharingIntentModule extends ReactContextBaseJavaModule {
    private final ReactApplicationContext reactContext;
    
    public FixedReceiveSharingIntentModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }
    
    @ReactMethod
    public void getReceivedFiles(Promise promise) {
        try {
            Activity currentActivity = getCurrentActivity();
            if (currentActivity == null) {
                promise.resolve(null);
                return;
            }
            
            Intent intent = currentActivity.getIntent();
            if (intent == null) {
                // Return empty result instead of throwing an exception
                promise.resolve(null);
                return;
            }
            
            // Now it's safe to access intent methods
            String action = intent.getAction();
            String type = intent.getType();
            
            // Only process if this is actually a sharing intent
            if (action == null || (!Intent.ACTION_SEND.equals(action) && !Intent.ACTION_VIEW.equals(action))) {
                promise.resolve(null);
                return;
            }
            
            // Process the intent safely
            // For now, just return null as this is a placeholder implementation
            promise.resolve(null);
        } catch (Exception e) {
            // Catch any unexpected errors and return null instead of crashing
            promise.resolve(null);
        }
    }
    
    @Override
    public String getName() {
        return "FixedReceiveSharingIntent";
    }
}