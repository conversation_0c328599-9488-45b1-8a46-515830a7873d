package com.svgviewerapp

import android.content.Intent
import android.os.Bundle
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "SvgViewerApp"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
      
  /**
   * Override onCreate to safely handle intents
   */
  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    // Safely handle the intent to prevent NullPointerException
    handleIntent(intent)
  }
  
  /**
   * Override onNewIntent to handle intents when app is already running
   */
  override fun onNewIntent(intent: Intent) {
    super.onNewIntent(intent)
    // Set the new intent and handle it safely
    setIntent(intent)
    handleIntent(intent)
  }
  
  /**
   * Safely handle the intent to prevent NullPointerException
   */
  private fun handleIntent(intent: Intent?) {
    // Check if intent is null before accessing its methods
    if (intent == null) {
      return
    }
    
    // Now it's safe to access intent.getAction() and other methods
    val action = intent.action
    // Additional intent handling can be done here
  }
}
